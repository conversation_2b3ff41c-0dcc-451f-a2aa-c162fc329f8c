import { http } from '@af-mobile-client-vue3/utils/http'

import { loginApi } from '@/services/api/Login'
import { get, post } from '@/services/restTools'

export interface BasicResponseModel<T = any> {
  code: number
  msg: string
  data: T
}

export function login(data: any) {
  return post(
    loginApi.Login,
    data,
  )
}

/**
 * @description: 获取用户信息
 */
export function getUserInfo() {
  return get(
    '/getUserInfo',
  )
}

/**
 * @description: 用户登出
 */
export function doLogout() {
  return http.request({
    url: loginApi.Logout,
    method: 'DELETE',
  })
}
