import { http } from '@af-mobile-client-vue3/utils/http'

interface ListResult {
  code: number
  message: string
  list: Array<any>
}

export function getListApi(params?: object): Promise<ListResult> {
  return http.request({
    url: '/list/get',
    method: 'get',
    params,
  })
}

export function getListApiError(data?: object): Promise<ListResult> {
  return http.request({
    url: '/list/error',
    method: 'post',
    data,
  })
}

export async function queryProse(): Promise<any> {
  return http.request({
    url: '/project/prose',
    method: 'post',
  })
}
