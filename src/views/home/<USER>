<script setup lang="ts">
import { getImageUrl } from '@af-mobile-client-vue3/utils/common'
</script>

<template>
  <div>
    <van-notice-bar class="notice" color="#1989fa" background="#ebf2fb" mode="link">
      <img class="notice_icon" :src="getImageUrl('../assets/img/home/<USER>/icon.png')">
      <span class="notice_title">
        消息中心<van-divider vertical :style="{ borderColor: '#1989fa' }" />
      </span>
      <span class="notice_content">
        您有0条未读消息
      </span>
    </van-notice-bar>
  </div>
</template>

<style scoped lang="less">
.notice {
  font-size: 14px;
  margin-bottom: var(--base-interval-1);
  .notice_icon {
    width: 30px;
    height: 30px;
    vertical-align: middle;
    position: relative;
    bottom: 2px;
  }
  .notice_title {
    padding-left: 4px;
    line-height: 50px;
  }
  .notice_content {
    color: #000;
  }
}
:deep(.van-notice-bar) {
  height: 50px;
  padding: 0 8px;
}
</style>
