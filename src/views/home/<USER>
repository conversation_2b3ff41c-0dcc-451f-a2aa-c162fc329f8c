<script setup lang="ts">
import { getImageUrl } from '@af-mobile-client-vue3/utils/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'

const apps = {
  测试图标1: {
    remark: '测试图标1',
    icon: 'assets/img/apps/safe-check-web.png',
  },
  测试图标2: {
    remark: '报装',
    icon: 'assets/img/apps/apply-web.png',
  },
  测试图标3: {
    remark: '营收',
    icon: 'assets/img/apps/revenue-web.png',
  },
  测试图标4: {
    remark: '物联网表',
    icon: 'assets/img/apps/iot-web.png',
  },
}

const images = [
  '../assets/img/home/<USER>',
  '../assets/img/home/<USER>',
  '../assets/img/home/<USER>',
  '../assets/img/home/<USER>',
]

interface Func {
  link: string
  navigate?: string
  module: any
  name: string
  children?: Array<Func>
}

interface App {
  module: Func
  icon: string
}

function initAppMenu(func: Array<Func> | undefined) {
  const functions = []
  if (func && func.length > 0) {
    for (let index = 0; index < func.length; index++) {
      const module = func[index]
      const app = apps[module.link]
      if (app) {
        functions.push({
          module,
          icon: app.icon,
        })
      }
    }
    return functions
  }
  return []
}

const appList: Array<App> = initAppMenu(useUserStore().getUserInfo().functions)
console.warn(appList)
</script>

<template>
  <div class="menu_main">
    <van-swipe class="my-swipe" :autoplay="3000" lazy-render>
      <van-swipe-item v-for="image in images" :key="image">
        <img :src="getImageUrl(image)" width="100%" height="100%">
      </van-swipe-item>
    </van-swipe>
    <van-grid :border="false">
      <van-grid-item v-for="app in appList" :key="app.module.link" :to="`app/${app.module.link}`">
        <img class="app_icon" :src="getImageUrl(app.icon, '../')">
        <span class="app_title">{{ app.module.name }}</span>
      </van-grid-item>
    </van-grid>
  </div>
</template>

<style scoped lang="less">
.menu_main {
  margin-bottom: var(--base-interval-2)
}

.my-swipe {
  margin-bottom: 4px;
  img {
    border-radius: 4px;
  }
}

:deep(.van-swipe__indicator--active) {
  width: 16px;
  background: #fff;
  border-radius: 16px;
}

.app_icon {
  width: 42px;
  height: 42px;
  margin-bottom: 6px;
}
.app_title {
  font-size: 12px;
  color: #000;
}
</style>
