<script setup lang="ts">
import {
  <PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
} from 'vant'
import { computed, defineEmits, defineProps, reactive, ref, watch } from 'vue'

const props = defineProps({
  columns: {
    type: Array,
    default() {
      return []
    },
  },
  option: {
    type: Object,
    default() {
      return { text: 'label', value: 'value', children: 'children' }
    },
  },
  isSearch: {
    type: Boolean,
    default: false,
  },
  offOption: { // 关闭option配置key-value;当数据是非集合的数组的时候，开启
    type: Boolean,
    default: false,
  },
  border: { // 是否展示边框
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['confirm', 'change', 'cancel', 'input'])
const { columns, option, isSearch, offOption } = props
const show = ref(false)
const searchVal = ref('')
const resultValue = ref(undefined)
let columnsData = reactive([])

const resultLabel = computed({
  get() {
    return resultValue.value
  },
  set() {

  },
})
function search(val) {
  if (val) {
    columnsData = columnsData.filter((item) => {
      const data = offOption ? item : item[option.text]

      return data.includes(val)
    })
  }
  else {
    columnsData = JSON.parse(JSON.stringify(columns))
  }
}
function onConfirm(value, index) {
  console.log(value)
  resultValue.value = offOption ? value.selectedValues : value.selectedValues[0]
  show.value = !show.value
  emits('confirm', value.selectedValues[0], value.selectedOptions)
}
function change(val, index) {
  emits('change', val, index, resultValue.value)
}
function cancel(val, index) {
  show.value = !show.value
  emits('cancel', val, index, resultValue.value)
}
function showPopu(disabled) {
  columnsData = JSON.parse(JSON.stringify(columns))
  // resultValue.value = `${selectValue}`
  if (disabled !== undefined && disabled !== false)
    return false
  else
    show.value = !show.value
}

// watch(() => selectValue, (newVal, _oldVal) => {
//   resultValue.value = `${newVal}`
// })
watch(() => resultValue, (newVal, _oldVal) => {
  searchVal.value = ''
  columnsData = JSON.parse(JSON.stringify(columns))
  emits('input', newVal)
})
</script>

<template>
  <VanField
    v-model="resultLabel"
    v-bind="$attrs"
    readonly
    :is-link="true"
    :border="border"
    error-message-align="right"
    @click="showPopu($attrs.disabled)"
  />
  <VanPopup v-model:show="show" position="bottom">
    <VanField v-if="isSearch" v-model="searchVal" input-align="left" placeholder="搜索" @input="search" />
    <VanPicker
      v-bind="$attrs"
      :columns="columnsData"
      :columns-field-names="option"
      show-toolbar
      :value-key="option.text"
      @cancel="cancel"
      @confirm="onConfirm"
      @change="change"
    />
  </VanPopup>
</template>

<style lang="scss" scoped>

</style>
