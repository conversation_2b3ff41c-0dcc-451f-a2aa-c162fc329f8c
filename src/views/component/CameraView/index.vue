<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue'
import { mobileUtil } from '@/views/example/example1/mobileUtil'

const { photoList } = defineProps({
  photoList: {
    type: Array,
    default() {
      return []
    },
  },
})
const emit = defineEmits(['change'])
const fileList = ref([])
const images = computed(() => {
  return {
    images: fileList.value.map((item) => {
      return item.content
    }),
    loop: false,
  }
})
/* photoList.map( item => {
  if(item){
    mobileUtil.execute({
      funcName:'convertImageToBase64',
      param: {filePath: item},
      callbackFunc: (result) => {
        console.log('base64.length===', result.content.length)
        console.log('item===', result.filePath)
        fileList.value.push({content:`data:image/png;base64,${result.content}`,filePath:result.filePath})
      }
    })
  }
}) */
function openCamera(file) {
  // 调用手机本地拍照
  mobileUtil.execute({
    funcName: 'photoAlbum',
    param: {
      watermark: '安全检查 - 张三',
    },
    callbackFunc: (results) => {
      results.forEach((result) => {
        console.log('result.length===', JSON.stringify(result))
        console.log('result.filePath===', result.filePath)
        console.log('原始大小===', result.originalSize)
        console.log('压缩后===', result.compressedSize)
        console.log('压缩率===', result.compressionRatio)

        fileList.value.push({ content: `data:image/png;base64,${result.content}`, filePath: result.filePath })
        emit('change', fileList.value.map((item) => {
          return item.filePath
        }))
      })
    },
  })
}
</script>

<template>
  <div>
    <van-uploader v-model="fileList" :preview-size="100" max-count="10" :preview-options="images" @click-upload="openCamera">
      <van-button icon="photograph" type="primary">
        拍照
      </van-button>
    </van-uploader>
  </div>
</template>

<style scoped lang="less">

</style>
