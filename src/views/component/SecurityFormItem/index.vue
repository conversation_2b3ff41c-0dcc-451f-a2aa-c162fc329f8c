<script setup lang="ts">
import XSignature from '@af-mobile-client-vue3/components/data/XSignature/index.vue'
import { defineEmits, defineProps } from 'vue'
import CameraView from '@/views/component/CameraView/index.vue'
import XMultiSelect from '@/views/component/XMultiSelect/index.vue'
import XSelect from '@/views/component/XSelect/index.vue'

const props = defineProps({
  item: {
    type: Object,
    required: false,
    default() {
      return {}
    },
  },
  // 传入item数据的结构
  dataStructure: {
    type: Object,
    default() {
      return {}
    },
  },
  formType: {
    type: Object,
    default() {
      return {}
    },
  },
})
const emit = defineEmits(['valueChange'])
const currentVal = props.item
const dataStructure = {
  fieldType: 'type',
  fieldLabel: 'label',
  fieldValue: 'value',
  placeholder: 'placeholder',
  fieldOptions: 'options',
  optionsLabel: 'label',
  optionsValue: 'value',
  ...props.dataStructure,
  // fieldType: 'type', // 表单项类型
  // fieldLabel: 'itemname',
  // fieldValue: 'value',
  // placeholder: 'itemname',
  // fieldOptions: 'options',
  // optionsLabel: 'data',
  // optionsValue: 'data',
}
const formType = {
  text: 'text', // 表单项类型
  number: 'number',
  date: 'date',
  radio: 'radio',
  checkbox: 'checkbox',
  multipleSelect: 'multipleSelect',
  select: 'select',
  picture: 'picture',
  textarea: 'textarea',
  signature: 'signature',
  ...props.formType,
}
const checkVal = ref([])
const showPicker = ref(false)
// selVal：组件返回值，selObj：选中的列表(单选/多选/下拉框/下多选拉框 有值 其他类型此参数为undefined)
function confirm(selVal, selObj) {
  switch (currentVal[dataStructure.fieldType]) {
    case formType.date:
      currentVal.value = selVal.selectedValues.join('-')
      selVal = currentVal.value
      showPicker.value = false
      break
    case formType.radio:
      selObj = currentVal[dataStructure.fieldOptions].filter((optRow) => {
        return optRow[dataStructure.optionsValue] === selVal
      })
      break
    case formType.checkbox:
      selObj = currentVal[dataStructure.fieldOptions].filter((optRow) => {
        return selVal.includes(optRow[dataStructure.optionsValue])
      })
      break
    case formType.multipleSelect:
      currentVal.value = selVal.join(',')
      break
    case formType.select:
      currentVal[dataStructure.fieldValue] = selVal
      break
    default:
      selVal = currentVal.value
  }
  emit('valueChange', selVal, selObj, currentVal)
}
// onMounted(() => {
// })
</script>

<template>
  <van-field
    v-if="currentVal[dataStructure.fieldType] === formType.text"
    v-model="currentVal[dataStructure.fieldValue]"
    center
    :name="currentVal[dataStructure.fieldLabel]"
    :label="currentVal[dataStructure.fieldLabel]"
    :placeholder="currentVal[dataStructure.placeholder]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请输入${props.item.itemname}` }]"
    @blur="confirm"
  >
    <!--    <template #input> -->
    <!--      <van-field -->
    <!--      v-if="currentVal[dataStructure.fieldType] === formType.picture" -->
    <!--      /> -->
    <!--    </template> -->
  </van-field>
  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.number"
    v-model="currentVal[dataStructure.fieldValue]"
    center
    :label="currentVal[dataStructure.fieldLabel]"
    type="number"
    :placeholder="currentVal[dataStructure.placeholder]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请输入${props.item.itemname}` }]"
    @blur="confirm"
  />
  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.date"
    v-model="currentVal[dataStructure.fieldValue]"
    center
    is-link
    readonly
    name="datePicker"
    :label="currentVal[dataStructure.fieldLabel]"
    :placeholder="currentVal[dataStructure.placeholder]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请输入${props.item.itemname}` }]"
    @click="showPicker = true"
  />
  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.textarea"
    v-model="currentVal[dataStructure.fieldValue]"
    type="textarea"
    center
    readonly
    name="textarea"
    :label="currentVal[dataStructure.fieldLabel]"
    :placeholder="currentVal[dataStructure.placeholder]"
    style="display: flex; flex-direction: column; align-items: flex-start; color: #336BF1;"
    @blur="confirm"
  />

  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.radio"
    center
    name="radio"
    :label="currentVal[dataStructure.fieldLabel]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请选择${props.item.itemname}` }]"
  >
    <template #input>
      <van-radio-group v-model="currentVal[dataStructure.fieldValue]" direction="horizontal" @change="confirm">
        <van-radio v-for="(opt, index) in currentVal[dataStructure.fieldOptions]" :key="index" class="checkbox-sty" :name="opt[dataStructure.optionsLabel]" :value="opt[dataStructure.optionsValue]">
          {{ opt[dataStructure.optionsLabel] }}
        </van-radio>
      </van-radio-group>
    </template>
  </van-field>
  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.checkbox"
    center
    name="checkboxGroup"
    :label="currentVal[dataStructure.fieldLabel]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请选择${props.item.itemname}` }]"
  >
    <template #input>
      <van-checkbox-group v-model="checkVal" direction="horizontal" @change="confirm">
        <van-checkbox v-for="(opt, index) in currentVal[dataStructure.fieldOptions]" :key="index" class="checkbox-sty" :name="opt[dataStructure.optionsValue]" shape="square">
          {{ opt[dataStructure.optionsLabel] }}
        </van-checkbox>
      </van-checkbox-group>
    </template>
  </van-field>

  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.signature"
    center
    name="signature"
    :label="currentVal[dataStructure.fieldLabel]"
  >
    <template #input>
      <div class="signature-demo">
        <XSignature
          v-model="currentVal[dataStructure.fieldValue]"
          :show-button-after-signed="true"
          :show-preview="true"
        />
        <div v-if="currentVal[dataStructure.fieldValue]" class="preview">
          <h5>预览签名</h5>
          <VanImage v-if="currentVal[dataStructure.fieldValue]" :src="currentVal[dataStructure.fieldValue]" />
        </div>
      </div>
    </template>
  </van-field>

  <XMultiSelect
    v-else-if="currentVal[dataStructure.fieldType] === formType.multipleSelect"
    v-model="currentVal[dataStructure.fieldValue]"
    center
    name="multipleSelect"
    :label="currentVal[dataStructure.fieldLabel]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请选择${props.item.itemname}` }]"
    :placeholder="currentVal[dataStructure.placeholder]"
    :columns="currentVal[dataStructure.fieldOptions]"
    @confirm="confirm"
  />

  <XSelect
    v-else-if="currentVal[dataStructure.fieldType] === formType.select"
    v-model="currentVal[dataStructure.fieldValue]"
    :placeholder="currentVal[dataStructure.placeholder]"
    :columns="currentVal[dataStructure.fieldOptions]"
    center
    name="select"
    :label="currentVal[dataStructure.fieldLabel]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请选择${props.item.itemname}` }]"
    @confirm="confirm"
  />

  <van-field
    v-else-if="currentVal[dataStructure.fieldType] === formType.picture"
    center
    name="picture"
    :label="currentVal[dataStructure.fieldLabel]"
    :rules="[{ required: props.item.checkmust === 'true', message: `请上传${props.item.itemname}` }]"
  >
    <template #input>
      <CameraView :photo-list="currentVal[dataStructure.fieldValue]" @change="confirm" />
    </template>
  </van-field>
  <van-popup v-model:show="showPicker" position="bottom">
    <van-date-picker @confirm="confirm" @cancel="showPicker = false" />
  </van-popup>
  <slot :item="currentVal" />
</template>

<style scoped lang="less">
.signature-demo {
  padding: 16px;
  .preview {
    margin-top: 20px;
    h5 {
      margin: 0 0 10px;
      font-size: 16px;
      color: #323233;
    }
    img {
      max-width: 100%;
      border: 1px solid #ebedf0;
      border-radius: 8px;
    }
  }
}

.checkbox-sty {
  padding: 3px;
}
</style>
