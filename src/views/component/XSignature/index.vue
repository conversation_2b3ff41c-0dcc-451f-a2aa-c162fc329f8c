<script setup lang="ts">
import XSignature from '@af-mobile-client-vue3/components/data/XSignature/index.vue'
import { Image as VanImage } from 'vant'
import { defineProps, ref } from 'vue'

const { itemName = '用户签字' } = defineProps<{
  itemName?: string
}>()
const signatureImage = ref('')
</script>

<template>
  <div>
    <van-field
      center
      name="signature"
      :label="itemName"
    >
      <template #input>
        <div class="signature-demo">
          <XSignature
            v-model="signatureImage"
            :show-button-after-signed="true"
            :show-preview="true"
          />
        </div>
      </template>
    </van-field>
    <div v-if="signatureImage" class="preview-container">
      <h5>预览签名</h5>
      <VanImage v-if="signatureImage" :src="signatureImage" />
    </div>
  </div>
</template>

<style scoped lang="less">
.signature-demo {
  padding: 16px;
  .preview {
    margin-top: 20px;
    h5 {
      margin: 0 0 10px;
      font-size: 16px;
      color: #323233;
    }
    img {
      max-width: 100%;
      border: 1px solid #ebedf0;
      border-radius: 8px;
    }
  }
}

.preview-container {
  background: white;
  padding: 16px;
  h5 {
    margin: 0 0 10px;
    font-size: 16px;
    color: #323233;
  }
  img {
    max-width: 100%;
    border: 1px solid #ebedf0;
    border-radius: 8px;
  }
}
</style>
