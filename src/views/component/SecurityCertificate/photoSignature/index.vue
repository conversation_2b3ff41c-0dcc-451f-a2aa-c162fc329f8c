<script setup lang="ts">
import { defineExpose, ref } from 'vue'
import XFormGroup from '@/views/component/XFormGroup/index.vue'
import signature from '@/views/component/XSignature/index.vue'

const xFormGroupRef = ref(null)
defineExpose({
  submit: () => {
    if (xFormGroupRef.value)
      return xFormGroupRef.value.submit()
  },
  validate: () => {
    if (xFormGroupRef.value) {
      if (typeof xFormGroupRef.value.validate === 'function')
        return xFormGroupRef.value.validate()
      return xFormGroupRef.value.submit()
    }
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject(['拍照签名未完成'])
  },
})
</script>

<template>
  <div class="test">
    <XFormGroup
      ref="xFormGroupRef"
      config-name="lngSecurityChecktestForm"
      service-name="af-safecheck"
      mode="新增"
    />
    <signature />
  </div>
</template>

<style scoped lang="less">

</style>
