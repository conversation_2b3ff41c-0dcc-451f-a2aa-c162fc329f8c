<script setup lang="ts">
import { onBeforeMount, ref } from 'vue'
import XFormGroup from '@/views/component/XFormGroup/index.vue'

const isInit = ref(false)
const formData = ref({})
onBeforeMount(() => {
  formData.value = {
    f_name: '王光明',
    f_userinfo_code: '12312432',
    f_user_type: '居民',
    f_state: '正常',
    f_address: '陕西省西安市雁塔区科技二路102号',
    f_certificate_type: '身份证',
    f_idnumber: '9837458220128740957',
    f_user_phone: '136-1234-5678',
    f_old_name: '光明王',
    f_population: '4',
    f_house_type: '楼房',
    f_user_level: '1级',
  }
  isInit.value = true
})
</script>

<template>
  <div class="test">
    <XFormGroup
      v-if="isInit"
      config-name="lngSecurityChestUserForm"
      service-name="af-safecheck"
      :group-form-data="formData"
      mode="新增"
    />
  </div>
</template>

<style scoped lang="less">

</style>
