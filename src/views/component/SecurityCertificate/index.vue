<script setup lang="ts">
import type { FormInstance } from 'vant'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { post } from '@af-mobile-client-vue3/services/restTools'
import {
  FloatingBubble,

  showConfirmDialog,
  showFailToast,
  But<PERSON> as <PERSON><PERSON>utton,
  Form as VanForm,
  Icon as VanIcon,
  Popover as VanPopover,
} from 'vant'
import { computed, nextTick, onBeforeMount, onBeforeUnmount, reactive, ref, watch } from 'vue'

import CameraView from '@/views/component/CameraView/index.vue'
import photoSignature from '@/views/component/SecurityCertificate/photoSignature/index.vue'
import userInfo from '@/views/component/SecurityCertificate/userInfo/index.vue'
import SecurityFormItem from '@/views/component/SecurityFormItem/index.vue'
import XSelect from '@/views/component/XSelect/index.vue'
import { mobileUtil } from '@/views/example/example1/mobileUtil'

// 浮动气泡
const offset = ref({ x: 310, y: 500 })
const floatingBubbleStatus = ref(false)

// 计算属性，从config.data中获取itemname作为下拉菜单的选项，并添加错误计数
const actions = ref([
  { text: '用户信息', value: 0, icon: '', errorCount: 0 },
])

const config = reactive({ data: [
  {
    itemname: '用气设备登记情况',
    checkmust: false,
    items: [
      {
        itemname: '燃气灶品牌大萨达撒多撒',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [],
        checkmust: 'false',
        type: 'string',
        value: '',
        f_aging: null,
      },
      {
        checkmust: 'false',
        itemname: '燃气灶安装时间',
        options: [],
        type: 'date',
        level: '级别1',
        f_process_mode: '自行处理',
        value: '2014-01-01',
        f_aging: '9999',
      },
      {
        checkmust: 'false',
        itemname: '燃气灶超期',
        options: [],
        type: 'string',
        level: '级别1',
        f_process_mode: '自行处理',
        value: '',
        f_aging: '9999',
      },
      {
        itemname: '壁挂炉品牌',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [],
        checkmust: 'false',
        value: '',
        type: 'string',
        f_aging: null,
      },
      {
        checkmust: 'false',
        itemname: '壁挂炉安装时间',
        options: [],
        type: 'date',
        level: '级别1',
        value: '',
        f_process_mode: '自行处理',
        f_aging: '9999',
      },
      {
        checkmust: 'false',
        itemname: '壁挂炉超期',
        options: [],
        type: 'string',
        level: '级别1',
        value: '',
        f_process_mode: '自行处理',
        f_aging: '9999',
      },
      {
        itemname: '热水器品牌',
        level: '级别3',
        f_process_mode: '自行处理',
        options: [],
        checkmust: 'false',
        type: 'string',
        value: '',
        f_aging: null,
      },
      {
        checkmust: 'false',
        itemname: '热水器安装时间',
        options: [],
        type: 'date',
        level: '级别1',
        value: '',
        f_process_mode: '自行处理',
        f_aging: '9999',
      },
      {
        checkmust: 'false',
        itemname: '热水器超期',
        options: [],
        type: 'string',
        level: '级别1',
        value: '',
        f_process_mode: '自行处理',
        f_aging: '9999',
      },
      {
        checkmust: 'true',
        itemname: '询问用户用气设备有无安装验收合格证',
        value: '',
        options: [
          {
            label: '有',
            value: '有',
            isdefault: true,
            isdefect: false,
          },
          {
            label: '无',
            value: '无',
            isdefault: false,
            isdefect: false,
          },
        ],
        type: 'selector',
        level: '级别3',
        f_process_mode: '现场整改',
        f_aging: null,
      },
      {
        checkmust: 'true',
        itemname: '购气折所用设备33',
        value: '',
        options: [
          {
            label: '碳烤炉',
            value: '碳烤炉',
            isdefault: true,
            isdefect: false,
          },
          {
            label: '双开门冰箱',
            value: '双开门冰箱',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '超级无敌美的高功率空调',
            value: '超级无敌美的高功率空调',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '也门胡塞武装',
            value: '也门胡塞武装',
            isdefault: false,
            isdefect: false,
          },
        ],
        type: 'radio',
        level: '级别3',
        f_process_mode: '自行处理',
        f_aging: null,
      },
      {
        checkmust: 'true',
        itemname: '购气所用设备',
        value: '',
        options: [
          {
            label: '碳烤炉',
            value: '碳烤炉1',
            isdefault: true,
            isdefect: false,
          },
          {
            label: '双开门冰箱',
            value: '双开门冰箱1',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '超级无敌美的高功率空调',
            value: '超级无敌美的高功率空调1',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '也门胡塞武装',
            value: '也门胡塞武装1',
            isdefault: false,
            isdefect: false,
          },
        ],
        type: 'checkbox',
        level: '级别3',
        f_process_mode: '自行处理',
        f_aging: null,
      },
      {
        checkmust: 'true',
        itemname: '购气所用设备',
        value: '',
        options: [
          {
            label: '碳烤炉',
            value: '碳烤炉',
            isdefault: true,
            isdefect: false,
          },
          {
            label: '双开门冰箱',
            value: '双开门冰箱',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '超级无敌美的高功率空调',
            value: '超级无敌美的高功率空调',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '也门胡塞武装',
            value: '也门胡塞武装',
            isdefault: false,
            isdefect: true,
          },
        ],
        type: 'select',
        level: '级别3',
        f_process_mode: '自行处理',
        f_aging: null,
      },
      {
        checkmust: 'true',
        itemname: '购气所用设备',
        value: '',
        options: [
          {
            label: '碳烤炉',
            value: '碳烤炉',
            isdefault: true,
            isdefect: false,
          },
          {
            label: '双开门冰箱',
            value: '双开门冰箱',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '超级无敌美的高功率空调',
            value: '超级无敌美的高功率空调',
            isdefault: false,
            isdefect: false,
          },
          {
            label: '也门胡塞武装',
            value: '也门胡塞武装',
            isdefault: false,
            isdefect: false,
          },
        ],
        type: 'multipleSelect',
        level: '级别3',
        f_process_mode: '自行处理',
        f_aging: null,
      },
      {
        checkmust: 'false',
        itemname: '热水器照片',
        value: ['/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/2ce5e0df-07d0-4a92-8ac6-3ffd67cefd5a.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/2f414ed1-8f15-456f-b053-50f472950cda.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/8c5542a2-8853-4e26-93cd-ebbf9d95fa42.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/75720a9d-ff66-41d5-898a-70ff44ca4191.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/40caf9d6-359e-4f24-99cd-0989f7de49a9.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/66332936-af49-4fc0-8b64-753f022f14d2.jpg'],
        options: [],
        type: 'picture',
        level: '级别1',
        f_process_mode: '自行处理',
        f_aging: '9999',
      },
    ],
  },
] })
const dataStructure = {
  fieldType: 'type', // 表单项类型
  fieldLabel: 'itemname',
  fieldValue: 'value',
  placeholder: 'itemname',
  fieldOptions: 'options',
  optionsLabel: 'data',
  optionsValue: 'data',
}
const formType = {
  text: 'string',
}
const status = ref(false)
onBeforeMount(() => {
  // 获取安检项
  post('api/af-safecheck/logic/getSecurityCheckTypeHistoryById', { id: 2 }).then((res: any) => {
    console.log('res====', res)
    if (res && res.f_json) {
      console.log('请求configdata====', JSON.parse(res.f_json))
      config.data = JSON.parse(res.f_json)
      console.log('测试configdata====', config.data)
      config.data.push({
        itemname: '用气设备登记情况',
        checkmust: false,
        items: [
          {
            itemname: '燃气灶品牌大萨达撒多撒',
            level: '级别3',
            f_process_mode: '自行处理',
            options: [],
            checkmust: 'false',
            type: 'string',
            value: '',
            f_aging: null,
          },
          {
            checkmust: 'false',
            itemname: '燃气灶安装时间',
            options: [],
            type: 'date',
            level: '级别1',
            f_process_mode: '自行处理',
            value: '2014-01-01',
            f_aging: '9999',
          },
          {
            checkmust: 'false',
            itemname: '燃气灶超期',
            options: [],
            type: 'string',
            level: '级别1',
            f_process_mode: '自行处理',
            value: '',
            f_aging: '9999',
          },
          {
            itemname: '壁挂炉品牌',
            level: '级别3',
            f_process_mode: '自行处理',
            options: [],
            checkmust: 'false',
            value: '',
            type: 'string',
            f_aging: null,
          },
          {
            checkmust: 'false',
            itemname: '壁挂炉安装时间',
            options: [],
            type: 'date',
            level: '级别1',
            value: '',
            f_process_mode: '自行处理',
            f_aging: '9999',
          },
          {
            checkmust: 'false',
            itemname: '壁挂炉超期',
            options: [],
            type: 'string',
            level: '级别1',
            value: '',
            f_process_mode: '自行处理',
            f_aging: '9999',
          },
          {
            itemname: '热水器品牌',
            level: '级别3',
            f_process_mode: '自行处理',
            options: [],
            checkmust: 'false',
            type: 'string',
            value: '',
            f_aging: null,
          },
          {
            checkmust: 'false',
            itemname: '热水器安装时间',
            options: [],
            type: 'date',
            level: '级别1',
            value: '',
            f_process_mode: '自行处理',
            f_aging: '9999',
          },
          {
            checkmust: 'false',
            itemname: '热水器超期',
            options: [],
            type: 'string',
            level: '级别1',
            value: '',
            f_process_mode: '自行处理',
            f_aging: '9999',
          },
          {
            checkmust: 'true',
            itemname: '询问用户用气设备有无安装验收合格证',
            value: '',
            options: [
              {
                data: '有',
                isdefault: true,
                isdefect: false,
              },
              {
                data: '无',
                isdefault: false,
                isdefect: false,
              },
            ],
            type: 'selector',
            level: '级别3',
            f_process_mode: '现场整改',
            f_aging: null,
          },
          {
            checkmust: 'true',
            itemname: '购气折所用设备33',
            value: '',
            options: [
              {
                data: '碳烤炉',
                isdefault: true,
                isdefect: false,
              },
              {
                data: '双开门冰箱',
                isdefault: false,
                isdefect: false,
              },
              {
                data: '超级无敌美的高功率空调',
                isdefault: false,
                isdefect: false,
              },
              {
                data: '也门胡塞武装',
                isdefault: false,
                isdefect: false,
              },
            ],
            type: 'radio',
            level: '级别3',
            f_process_mode: '自行处理',
            f_aging: null,
          },
          {
            checkmust: 'true',
            itemname: '购气所用设备',
            value: '',
            options: [
              {
                data: '碳烤炉',
                isdefault: true,
                isdefect: false,
              },
              {
                data: '双开门冰箱',
                isdefault: false,
                isdefect: false,
              },
              {
                data: '超级无敌美的高功率空调',
                isdefault: false,
                isdefect: false,
              },
              {
                data: '也门胡塞武装',
                isdefault: false,
                isdefect: false,
              },
            ],
            type: 'checkbox',
            level: '级别3',
            f_process_mode: '自行处理',
            f_aging: null,
          },
          {
            checkmust: 'true',
            itemname: '购气所用设备',
            value: '',
            options: [
              {
                label: '碳烤炉',
                value: '碳烤炉',
                isdefault: true,
                isdefect: false,
              },
              {
                label: '双开门冰箱',
                value: '双开门冰箱',
                isdefault: false,
                isdefect: false,
              },
              {
                label: '超级无敌美的高功率空调',
                value: '超级无敌美的高功率空调',
                isdefault: false,
                isdefect: false,
              },
              {
                label: '也门胡塞武装',
                value: '也门胡塞武装',
                isdefault: false,
                isdefect: false,
              },
            ],
            type: 'select',
            level: '级别3',
            f_process_mode: '自行处理',
            f_aging: null,
          },
          {
            checkmust: 'true',
            itemname: '购气所用设备',
            value: '',
            options: [
              {
                label: '碳烤炉',
                value: '碳烤炉',
                isdefault: true,
                isdefect: false,
              },
              {
                label: '双开门冰箱',
                value: '双开门冰箱',
                isdefault: false,
                isdefect: false,
              },
              {
                label: '超级无敌美的高功率空调',
                value: '超级无敌美的高功率空调',
                isdefault: false,
                isdefect: false,
              },
              {
                label: '也门胡塞武装',
                value: '也门胡塞武装',
                isdefault: false,
                isdefect: false,
              },
            ],
            type: 'multipleSelect',
            level: '级别3',
            f_process_mode: '自行处理',
            f_aging: null,
          },
          {
            checkmust: 'false',
            itemname: '热水器照片',
            value: ['/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/2ce5e0df-07d0-4a92-8ac6-3ffd67cefd5a.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/2f414ed1-8f15-456f-b053-50f472950cda.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/8c5542a2-8853-4e26-93cd-ebbf9d95fa42.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/75720a9d-ff66-41d5-898a-70ff44ca4191.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/40caf9d6-359e-4f24-99cd-0989f7de49a9.jpg', '/data/user/0/com.af.v4.view.af_mobile_view/app_flutter/images/66332936-af49-4fc0-8b64-753f022f14d2.jpg'],
            options: [],
            type: 'picture',
            level: '级别1',
            f_process_mode: '自行处理',
            f_aging: '9999',
          },
        ],
      })
      // 更新actions数据，从config.data中获取itemname
      updateActions()
      status.value = true
    }
    console.log(config)
  })
})

// 修改为使用对象管理多个表单引用
const formRefs = ref<FormInstance[]>([])
// 当前选中的tab索引
const activeTabIndex = ref(0)
// 子组件实例
const photoSignatureRef = ref(null)

// 添加错误计数追踪
const errorCounts = reactive({
  userInfo: 0,
  tabs: [] as number[],
  photoSignature: 0,
})

// 确保所有标签页被初始化的函数
async function initAllTabs() {
  console.log('初始化所有标签页')
  // 保存当前活动标签页
  const currentTab = activeTabIndex.value

  // 遍历激活每个标签页
  for (let i = 0; i < actions.value.length; i++) {
    activeTabIndex.value = i
    await nextTick() // nextTick确保DOM完全更新
  }

  // 恢复原来的标签页
  activeTabIndex.value = currentTab
  await nextTick()
}

// 当状态变为true时（数据加载完成后）初始化所有标签页
watch(() => status.value, async (newVal) => {
  if (newVal) {
    await nextTick()
    await initAllTabs()
    // 初始化错误计数数组
    errorCounts.tabs = Array.from({ length: config.data.length }).fill(0) as number[]
  }
})

// 验证所有表单并更新错误计数
async function validateAllForms() {
  // 重置所有错误计数
  errorCounts.userInfo = 0
  errorCounts.tabs.fill(0)
  errorCounts.photoSignature = 0

  // 存储错误信息，以便在提示时使用
  let firstErrorMessage = ''

  // 再次确保所有标签页已初始化
  await initAllTabs()

  // 验证常规表单
  for (let i = 0; i < formRefs.value.length; i++) {
    const formInstance = formRefs.value[i]
    if (formInstance) {
      try {
        await formInstance.validate()
      }
      catch (error) {
        // 更新错误计数
        errorCounts.tabs[i] = error.length || 1
        // 记录第一条错误信息
        if (!firstErrorMessage && error && error[0])
          firstErrorMessage = error[0]
      }
    }
  }

  // 验证拍照签名组件
  if (photoSignatureRef.value) {
    try {
      await photoSignatureRef.value.validate()
    }
    catch (error) {
      errorCounts.photoSignature = error.length || 1
      // 如果还没有记录错误信息，记录拍照签名的第一条错误
      if (!firstErrorMessage && error && error[0])
        firstErrorMessage = error[0]
    }
  }

  return { hasErrors: errorCounts.userInfo > 0
    || errorCounts.tabs.some(count => count > 0)
    || errorCounts.photoSignature > 0, firstErrorMessage }
}

// 提交表单时的处理逻辑
async function onSubmit() {
  console.log(config)
  try {
    // 验证所有表单并更新错误计数
    const { hasErrors, firstErrorMessage } = await validateAllForms()

    if (hasErrors) {
      // 找到第一个有错误的标签页并跳转
      if (errorCounts.userInfo > 0) {
        activeTabIndex.value = 0
      }
      else {
        const firstErrorIndex = errorCounts.tabs.findIndex(count => count > 0)
        if (firstErrorIndex >= 0)
          activeTabIndex.value = firstErrorIndex + 1 // +1 因为第0个是用户信息
        else if (errorCounts.photoSignature > 0)

          activeTabIndex.value = actions.value.length - 1
      }

      // 显示具体的错误信息，而不是通用提示
      showFailToast(firstErrorMessage || '请填写所有的必填项')
      return
    }

    // 如果所有表单校验通过，则弹出确认框
    showConfirmDialog({
      title: '服务',
      message: '请确认是否提交？',
    })
      .then(() => {
        // 用户确认提交
        history.back()
      })
      .catch(() => {
        // 用户取消提交
      })
  }
  catch (error) {
    // 如果有表单校验未通过，则提示用户
    console.error('表单校验未通过:', error)
    showFailToast('请填写所有的必填项！！！')
  }
}

// 接口定义
interface ActionItem {
  text: string
  value: number
  icon: string
  errorCount: number
}

// 更新actions数据的函数
function updateActions() {
  // 清空actions，保留第一个"用户信息"选项
  actions.value = [{ text: '用户信息', value: 0, icon: '', errorCount: 0 }]
  // actions.value = []
  // 从config.data中获取itemname，添加到actions中
  config.data.forEach((item, index) => {
    actions.value.push({
      text: item.itemname,
      value: index + 1, // 索引+1，因为第0个是"用户信息"
      icon: '', // 添加空的icon属性以满足类型要求
      errorCount: 0,
    })
  })
  actions.value.push({
    text: '拍照签名',
    value: actions.value.length, // 最后一个
    icon: '', // 添加空的icon属性以满足类型要求
    errorCount: 0,
  })
}

// 添加处理方式选项常量
const columns = [
  { label: '现场处理', value: '现场处理' },
  { label: '自行整改', value: '自行整改' },
  { label: '转维修', value: '转维修' },
]

// 更新actions中的错误计数的计算属性
const updatedActions = computed(() => {
  return actions.value.map((action, index) => {
    if (index === 0) {
      // 用户信息
      return {
        ...action,
        errorCount: errorCounts.userInfo,
      }
    }
    else if (index === actions.value.length - 1) {
      // 拍照签名
      return {
        ...action,
        errorCount: errorCounts.photoSignature,
      }
    }
    else {
      // 其他标签页
      return {
        ...action,
        errorCount: errorCounts.tabs[index - 1] || 0,
      }
    }
  })
})

function valueChange(selVal, selObj, currentVal) {
  console.log('父组件接到了')
  console.log(selVal)
  console.log(selObj)
  switch (currentVal.type) {
    case 'radio' :
    case 'select' :
    case 'checkbox' :
    case 'multipleSelect':
      console.log('?????', selObj[0].isdefect)
      currentVal.f_is_defect = selObj[0].isdefect
      break
  }
  console.log(currentVal)
}

function processModeChange(selVal, selObj) {
  console.log(selVal)
  console.log(selObj)
}
function cameraViewChange(list) {
  console.log('list', list)
}

function change(value) {
  console.log(value)
  // 设置当前选中的tab索引
  activeTabIndex.value = value
}
const checked2 = ref([])
function audio(isRecord: boolean) {
  if (isRecord) {
    mobileUtil.execute({
      funcName: 'makePhoneCall',
      param: {
        phoneNumber: '13209745328',
      },
      callbackFunc: (result) => {
        console.log('reslut====', JSON.stringify(result))
      },
    })
  }
  else {
    mobileUtil.execute({
      funcName: 'showSignaturePad',
      param: {},
      callbackFunc: (result) => {
        console.log('reslut====', JSON.stringify(result))
      },
    })
  }
}

// 获取 van-tabs 的实例
const tabsRef = ref(null)
function slideLeft() {
  if (activeTabIndex.value === 0)
    activeTabIndex.value = actions.value.length - 1
  else
    activeTabIndex.value = activeTabIndex.value - 1
  if (tabsRef.value)
    (tabsRef.value as any).scrollTo(activeTabIndex.value)
}
function slideRight() {
  if (activeTabIndex.value === actions.value.length - 1) {
    console.log('最后一页--------保存-------该你实现事件了')
    showFailToast('已经是最后一页了')
  }
  else {
    activeTabIndex.value = activeTabIndex.value + 1
    if (tabsRef.value)
      (tabsRef.value as any).scrollTo(activeTabIndex.value)
  }
}
// 气泡弹出层按钮状态
const tabStatus = ref(false)
function PageSwitch(event) {
  // 设置当前选中的tab索引
  activeTabIndex.value = event.value
  if (tabsRef.value)
    (tabsRef.value as any).scrollTo(event.value)
}

// 气泡弹出层的图标
const popoverIcon = computed(() => {
  return tabStatus.value ? 'arrow-down' : 'arrow-up'
})

// 底部按钮的样式
const buttonType = computed(() => {
  return activeTabIndex.value === actions.value.length - 1 ? 'success' : 'primary'
})
const buttonText = computed(() => {
  return activeTabIndex.value === actions.value.length - 1 ? '保 存' : '下一页'
})
const buttonIcon = computed(() => {
  return activeTabIndex.value === actions.value.length - 1 ? 'success' : 'arrow'
})

// 菜单位置计算
const menuPosition = computed(() => {
  // 气泡直径和菜单宽度
  const bubbleSize = 55
  const menuWidth = 140

  // 计算菜单位置，使菜单居中在气泡上方
  // 确保菜单左侧与气泡中心对齐
  const menuLeft = offset.value.x - (menuWidth / 2) + (bubbleSize / 2)

  // 添加安全距离，确保菜单不会超出屏幕边缘
  const safeMenuLeft = Math.max(10, Math.min(window.innerWidth - menuWidth - 10, menuLeft))

  return {
    left: `${safeMenuLeft}px`,
    top: `${offset.value.y - 160}px`, // 在气泡正上方，留出一些间距
    position: 'fixed' as const,
    zIndex: 2001,
  }
})

// 悬浮气泡菜单项
const floatingBubbleActions = ref([
  { text: '用户360', icon: 'contact', value: '用户360' },
  { text: '清空临时保存', icon: 'delete', value: '清空临时保存' },
  { text: '返回首页', icon: 'home-o', value: '返回首页' },
])

// 浮动气泡点击事件
function onFloatingBubble() {
  floatingBubbleStatus.value = !floatingBubbleStatus.value
}

function floatingBubbleSwitch(action) {
  console.log('选择的操作：', action)
  switch (action.value) {
    case '用户360':
      // 跳转到用户360页面
      mobileUtil.execute({
        funcName: 'navigateTo',
        param: {
          path: '/userProfile',
        },
        callbackFunc: (result) => {
          console.log('navigateTo result:', JSON.stringify(result))
        },
      })
      break
    case '清空临时保存':
      // 清空临时保存的数据
      showConfirmDialog({
        title: '提示',
        message: '确定要清空临时保存的数据吗？',
      }).then(() => {
        // 清空逻辑
        showFailToast('临时数据已清空')
      }).catch(() => {
        // 取消操作
      })
      break
    case '返回首页':
      // 返回首页
      mobileUtil.execute({
        funcName: 'navigateTo',
        param: {
          path: '/',
        },
        callbackFunc: (result) => {
          console.log('navigateTo result:', JSON.stringify(result))
        },
      })
      break
    default:
      showFailToast('功能开发中')
  }
  // 关闭气泡
  floatingBubbleStatus.value = false
}

// 添加点击外部关闭菜单的功能
watch(() => floatingBubbleStatus.value, (newVal) => {
  if (newVal) {
    // 延迟添加事件，避免同一次点击立即关闭
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick)
    }, 10)
  }
  else {
    document.removeEventListener('click', handleOutsideClick)
  }
})

function handleOutsideClick(e) {
  const bubble = document.querySelector('.van-floating-bubble')
  const menu = document.querySelector('.bubble-menu-container')

  // 如果点击不在气泡或菜单内，则关闭菜单
  if (bubble && !bubble.contains(e.target) && menu && !menu.contains(e.target))
    floatingBubbleStatus.value = false
}

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<template>
  <NormalDataLayout v-if="status" id="111" title="入户安检">
    <template #layout_content>
      <div class="tabs-container">
        <!-- 标签页容器 -->
        <div class="tabs-wrapper">
          <van-tabs ref="tabsRef" v-model:active="activeTabIndex" :ellipsis="false" class="custom-tabs">
            <van-tab>
              <template #title>
                <div class="tab-title">
                  <span>用户信息</span>
                  <span v-if="errorCounts.userInfo > 0" class="error-badge">{{ errorCounts.userInfo }}</span>
                </div>
              </template>
              <div class="x-form-group">
                <div class="x-form-title">
                  <VanIcon name="gem-o" />{{ '用户信息' }}
                </div>
                <userInfo />
              </div>
            </van-tab>
            <template v-for="(row, index) in config.data" :key="row.itemname + index">
              <van-tab>
                <template #title>
                  <div class="tab-title">
                    <span>{{ row.itemname }}</span>
                    <span v-if="errorCounts.tabs[index] > 0" class="error-badge">{{ errorCounts.tabs[index] }}</span>
                  </div>
                </template>
                <div class="x-form-group-item">
                  <VanForm :ref="(el) => { if (el) formRefs[index] = el as any }" @submit="onSubmit">
                    <van-cell-group>
                      <div class="x-form-title">
                        <VanIcon name="gem-o" />{{ row.itemname }}
                      </div>
                      <template v-for="(item, index) in row.items" :key="row.itemname + index">
                        <SecurityFormItem
                          :item="item"
                          :data-structure="dataStructure"
                          :form-type="formType"
                          @value-change="valueChange"
                        >
                          <template v-if="(item as any).f_is_defect === true" #default="{ item }">
                            <van-field
                              name="handlingMethod"
                              center
                              label="处理方式"
                              :rules="[{ required: item.checkmust === 'true', message: '请选择处理方式' }]"
                            >
                              <template #input>
                                <XSelect
                                  v-model="item.f_process_mode"
                                  placeholder="请选择处理方式"
                                  :columns="columns"
                                  @confirm="processModeChange"
                                />
                              </template>
                            </van-field>
                            <van-field
                              name="picture"
                              center
                              label="隐患照片"
                              :rules="[{ required: item.checkmust === 'true', message: '请上传隐患照片' }]"
                            >
                              <template #input>
                                <CameraView :photo-list="item.f_defect_paths" @change="cameraViewChange" />
                              </template>
                            </van-field>
                          </template>
                        </SecurityFormItem>
                      </template>
                    </van-cell-group>
                  </VanForm>
                </div>
              </van-tab>
            </template>
            <van-tab>
              <template #title>
                <div class="tab-title">
                  <span>拍照签名</span>
                  <span v-if="errorCounts.photoSignature > 0" class="error-badge">{{ errorCounts.photoSignature }}</span>
                </div>
              </template>
              <div class="x-form-group">
                <div class="x-form-title">
                  <VanIcon name="gem-o" />{{ '拍照签名' }}
                </div>
                <photoSignature ref="photoSignatureRef" />
              </div>
            </van-tab>
          </van-tabs>
        </div>
        <!-- 固定在标签页标题行右侧的 VanPopover -->
        <div class="fixed-popover-container">
          <VanPopover
            v-model:show="tabStatus"
            :actions="updatedActions"
            placement="bottom-end"
            @select="PageSwitch($event)"
          >
            <template #reference>
              <VanButton :icon="popoverIcon" icon-position="left" type="default" size="mini" class="custom-button" />
            </template>
            <template #action="{ action }">
              <div class="custom-popover-action">
                <span class="popover-action-text">{{ action.text }}</span>
                <span v-if="action.errorCount > 0" class="error-badge">{{ action.errorCount }}</span>
              </div>
            </template>
          </VanPopover>
        </div>
      </div>
      <!-- 新增底部按钮区域 -->
      <div class="footer-buttons">
        <VanButton icon="passed" type="primary" block @click="onSubmit">
          提交
        </VanButton>
        <div class="slide-buttons">
          <VanButton type="default" plain icon="arrow-left" text="上一步" @click="slideLeft" />
          <VanButton :type="buttonType" normal @click="slideRight">
            {{ buttonText }}<VanIcon :name="buttonIcon" />
          </VanButton>
        </div>
      </div>

      <!-- 悬浮气泡 -->
      <FloatingBubble
        v-model:offset="offset"
        icon="service"
        axis="xy"
        magnetic="x"
        @click="onFloatingBubble"
      />

      <!-- 自定义悬浮菜单 -->
      <teleport to="body">
        <div v-if="floatingBubbleStatus" class="bubble-menu-container" :style="menuPosition">
          <div class="bubble-menu">
            <div
              v-for="(action, index) in floatingBubbleActions"
              :key="index"
              class="bubble-menu-item"
              @click="floatingBubbleSwitch(action)"
            >
              <VanIcon :name="action.icon" class="bubble-menu-icon" />
              <span>{{ action.text }}</span>
            </div>
          </div>
        </div>
      </teleport>
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
.tabs-container {
  height: calc(100vh - 120px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative; /* 使 fixed-popover-container 相对于 tabs-container 定位 */
}

.tabs-wrapper {
  width: 100%; /* 确保 van-tabs 占据整个宽度 */
  background-color: rgb(247, 248, 250);
  padding-bottom: 10px;
  .x-form-group-item {
    margin: 20px 0;
  }
}

.x-form-group {
  margin: 20px 0;
  background-color: rgb(247, 248, 250);
}

.custom-tabs {
  :deep(.van-tabs__nav) {
    padding-right: 45px; /* 在标题栏右侧留出30px的空间 */
  }

  :deep(.van-tab) {
    padding-right: 10px; /* 为标题右侧的徽标留出更多空间 */
  }
}

.x-form-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  background-color: #fff;
  padding: 10px 0 10px 15px;
  margin-left: 0; // 修改左边距为0
}

.fixed-popover-container {
  position: absolute; /* 绝对定位 */
  top: 5px; /* 与标签页标题行顶部对齐 */
  right: 0; /* 固定在右侧 */
  z-index: 1000; /* 确保在其他内容之上 */
  margin-right: -1px; /* 右侧间距 */
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: start;
  padding: 16px;
  position: fixed; /* 固定位置 */
  bottom: 40px; /* 距离底部40像素 */
  width: 100%; /* 宽度占满整个页面 */
  background-color: white; /* 背景颜色，防止内容被遮挡 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影，提升视觉效果 */

  .van-button--block {
    width: 40%;
  }

  .slide-buttons {
    display: flex;
    gap: 8px;
  }
}

.custom-button {
  border: none !important;
  box-shadow: none !important;
  pointer-events: none; // 禁用点击反馈
  :deep(.van-icon) {
    // margin-top: 10px; /* 调整数值以达到所需效果 */
    vertical-align: middle;
    margin-right: 10px;
    margin-left: 10px;
  }
}

.error-badge {
  display: inline-block;
  background-color: red;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  height: 16px;
  width: 16px;
  line-height: 16px;
  text-align: center;
  margin-left: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加阴影增强可见性 */
}

.custom-popover-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 8px;

  .popover-action-text {
    flex: 1;
    text-align: left;
    margin-right: 8px;
  }

  .error-badge {
    font-size: 12px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    flex-shrink: 0; /* 防止徽标被挤压变形 */
  }
}

:deep(.van-popover__action) {
  height: auto;
  padding: 8px;
}

:deep(.van-popover__action-text) {
  justify-content: left !important;
}

.tab-title {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 3px; /* 添加一些内边距 */
  min-height: 20px; /* 确保标题有足够高度 */

  .error-badge {
    position: absolute;
    top: -2px; /* 微调顶部位置 */
    right: -10px; /* 微调右侧位置 */
    margin-left: 0;
    z-index: 10; /* 确保徽标始终显示在顶层 */
    font-size: 10px; /* 右上角位置徽标稍微小一点 */
    height: 14px;
    width: 14px;
    line-height: 14px;
  }
}

/* 气泡样式 */
:deep(.van-floating-bubble) {
  --van-floating-bubble-size: 55px;
  --van-floating-bubble-background: #1989fa;
  --van-floating-bubble-color: #fff;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

:deep(.van-floating-bubble__icon) {
  font-size: 26px;
}

/* 气泡菜单样式 */
.bubble-menu-container {
  position: fixed;
  z-index: 2000;
}

.bubble-menu {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  min-width: 140px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

.bubble-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 20px;
  color: #323233;
  cursor: pointer;

  &:active {
    background-color: #f2f3f5;
  }

  &:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }
}

.bubble-menu-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #1989fa;
}
</style>
