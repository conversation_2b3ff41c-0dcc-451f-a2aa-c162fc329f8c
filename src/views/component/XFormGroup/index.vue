<script setup lang="ts">
import XForm from '@af-mobile-client-vue3/components/data/XForm/index.vue'
import { getConfigByName } from '@af-mobile-client-vue3/services/api/common'
import {
  Tab as VanTab,
  Tabs as VanTabs,
} from 'vant'
import { defineEmits, defineProps, onBeforeMount, ref, watch } from 'vue'

const props = withDefaults(defineProps<{
  configName?: string
  serviceName?: string
  groupFormData?: object
  mode?: string
}>(), {
  configName: '',
  serviceName: undefined,
  groupFormData: () => ({}),
  mode: '查询',
})
const emit = defineEmits(['submit'])

interface Form {
  configName?: string
  serviceName?: string
  groupFormData?: object
  mode?: string
}

const groupItems = ref([])
const formData = ref({})
const submitGroup = ref(false)
const submitSimple = ref(false)
const isInit = ref(false)
const initStatus = ref(false)
const propsData = ref({} as any)

// 组件初始化函数
function init(params: Form) {
  initStatus.value = true
  propsData.value = {
    // configName: '',
    // serviceName: undefined,
    // groupFormData: () => ({}),
    // mode: '查询',
    configName: props.configName,
    serviceName: props.serviceName,
    groupFormData: props.groupFormData,
    mode: props.mode,
    ...params,
  }
  formData.value = propsData.value.groupFormData
  getConfigByName(propsData.value.configName, (result) => {
    console.log('result===', result)
    if (result?.groups) {
      submitGroup.value = true
      groupItems.value = result.groups
      result.groups.forEach((group) => {
        if (!formData.value[group.groupName])
          formData.value[group.groupName] = {}
      })
    }
    else {
      submitSimple.value = result.showSubmitBtn
      groupItems.value = [{ ...result }]
    }
    isInit.value = true
  }, propsData.value.serviceName)
}
watch(() => props.groupFormData, (_val) => {
  formData.value = { ...formData.value, ...props.groupFormData }
})
onBeforeMount(() => {
  if (!initStatus.value)
    init(props)
})
const xFormListRef = ref([])
async function submit() {
  for (const res of xFormListRef.value) {
    await res.validate()
    formData.value[res.formGroupName] = res.form
  }
  emit('submit', formData.value)
}

// function initXForm(index: number) {
// 获取自身示例
// refs[`xFormListRef-${index}`].init({})
// }

defineExpose({ init, submit })
</script>

<template>
  <div v-if="isInit" id="x-form-group">
    <VanTabs :show-header="false" scrollspy sticky>
      <VanTab
        v-for="(item, index) in groupItems"
        :key="item.groupName ? (item.groupName + index) : index"
        :title="item.describe ? item.describe : item.tableName "
      >
        <div class="x-form-group-item">
          <!--          :ref="`xFormListRef-${index}`" -->
          <XForm
            ref="xFormListRef"
            class="x-form"
            :mode="props.mode"
            :group-form-items="item"
            :form-data="item.groupName ? formData[item.groupName] : formData"
            :form-name="item.groupName"
            :service-name="props.serviceName"
            :submit-button="false"
            @on-submit="submit"
          />
        </div>
      </VanTab>
    </VanTabs>
    <!--    <VanButton v-if="submitGroup" round block type="primary" @click="submit"> -->
    <!--      提交 -->
    <!--    </VanButton> -->
  </div>
</template>

<style scoped lang="less">
#x-form-group {
  //background-color: rgb(247,248,250);
  .x-form-group-item {
    //margin:20px 0;
    background-color: white;
  }
  .x-form {
    margin-left: -15px; /* 根据需要调整左移的距离 */
  }
}
</style>
