<script setup lang="ts">
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { runLogic } from '@/services/api/common'

// 访问配置名
const configName = ref('checkplanlistCRUD')
// 访问服务名
const serviceName = ref('af-safecheck')

// 定义接口
interface CardData {
  title: string
  number: number | string
  color: string
  backgroundColor: string
}

// 创建响应式数组
const myArray = reactive<CardData[]>([
  { title: '计划总数', number: 100, color: 'rgb(64,109,236)', backgroundColor: 'rgba(239,256,255)' },
  { title: '已检数', number: 45, color: '#30B620', backgroundColor: 'rgba(240,253,244)' },
  { title: '未检数', number: 55, color: '#B08E0F', backgroundColor: 'rgba(254,252,232)' },
  { title: '入户率', number: '45%', color: '#9932CC', backgroundColor: 'rgba(250,245,255)' },
  { title: '完成率', number: '45%', color: '#FF6A6A', backgroundColor: 'rgb(254,242,242)' },
])

async function getData() {
  const res = await runLogic('securityCheckResultsLOGIC', {}, 'af-safecheck')
  myArray[0].number = ((res as unknown) as { securityCheckNumber: { num: number }[] }).securityCheckNumber[0]?.num || 0
  myArray[1].number = ((res as unknown) as { numberOfHouseholds: { num: number }[] }).numberOfHouseholds[0]?.num || 0
  myArray[2].number = ((res as unknown) as { numberOfUploads: { num: number }[] }).numberOfUploads[0]?.num || 0
  myArray[3].number = ((res as unknown) as { thePenetrationRate: { result: string }[] }).thePenetrationRate[0]?.result || '0%'
  myArray[4].number = ((res as unknown) as { completionRate: { result: string }[] }).completionRate[0]?.result || '0%'
}

onMounted(() => {
  getData()
})
</script>

<template>
  <NormalDataLayout id="123" title="安检计划详情">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :service-name="serviceName">
        <template #search-after>
          <div class="custom-content">
            <div class="core">
              <!--  <van-row class="header_row">
                <van-col span="18">
                  <span class="plan_title">2024年第一季度安检计划</span>
                  <span class="plan-date">2024-01-01 至 2024-03-31</span>
                </van-col>
                <van-col span="6" style="text-align: right;">
                  <span class="plan-status">进行中</span>
                </van-col>
              </van-row> -->
              <van-row class="core_row">
                <van-col v-for="item in myArray" :key="item.title">
                  <div class="stat-item" :style="{ backgroundColor: item.backgroundColor }">
                    <p class="core_title" :style="{ color: item.color }">
                      {{ item.title }}
                    </p>
                    <p class="core_value" :style="{ color: item.color }">
                      {{ item.number }}
                    </p>
                  </div>
                </van-col>
              </van-row>
            </div>
          </div>
        </template>
      </XCellList>
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
.custom-content {
  padding: 0 13px;
  margin-top: -10px;

  .core {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header_row {
      margin-bottom: 12px;
      .plan-date {
        color: #666;
        font-size: 12px;
        font-weight: 600;
        display: block;
        margin: 0;
      }
      .plan_title {
        font-weight: 600;
      }
      .plan-status {
        background-color: rgba(219,234,254);
        color: rgb(62, 92, 211);
        padding: 5px 8px;
        border-radius: 15px;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .core_row {
      display: flex;
      justify-content: space-between;
      text-align: center;
      gap: 8px;

      .van-col {
        flex: 1;
        .stat-item {
          padding: 8px 4px;
          border-radius: 4px;
        }

        .core_title {
          font-size: 12px;
          margin: 0 0 8px;
        }

        .core_value {
          font-size: 16px;
          font-weight: bold;
          margin: 0;
        }
      }
    }
  }
}
</style>
