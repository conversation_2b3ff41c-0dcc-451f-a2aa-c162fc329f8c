<template>
  <div class="page-bg">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="用户预约"
      fixed
      placeholder
      :border="false"
    >
      <template #right>
        <img src="@/assets/logo.png" alt="Logo" class="logo-icon">
      </template>
    </van-nav-bar>

    <!-- 主要内容区域 -->
    <div>
      <!-- 预约须知卡片 -->
      <div class="notice-card">
        <div class="notice-header">
          <div class="icon-wrapper">
            <van-icon name="notes-o" color="#7fdbf8" size="24" />
          </div>
          <h2 class="notice-title">安检预约须知</h2>
        </div>

        <!-- 重要限制 -->
<!--        <div class="notice-section">-->
<!--          <h3 class="section-title">-->
<!--            <van-icon name="warning" color="#f59e0b" class="mr-2" />-->
<!--            重要限制-->
<!--          </h3>-->
<!--          <div class="notice-list">-->
<!--            <div class="notice-item warning">-->
<!--              <van-icon name="close" color="#f59e0b" class="mr-2" />-->
<!--              <span>用户两年内已安检过或非民用用户，无需进行安检预约操作。</span>-->
<!--            </div>-->
<!--            <div class="notice-item warning">-->
<!--              <van-icon name="close" color="#f59e0b" class="mr-2" />-->
<!--              <span>每日预约名额限制为100次，请及时预约。</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

        <!-- 温馨提示 -->
        <div class="notice-section">
          <h3 class="section-title">
            <van-icon name="info" color="#3b82f6" class="mr-2" />
            温馨提示
<!--            <span class="customer-service-phone">统一客服电话：96777</span>-->
          </h3>
          <div class="notice-list">
            <div v-for="(tip, index) in tips" :key="index" class="notice-item">
              <van-icon name="checked" color="#3b82f6" class="mr-2" />
              <span>{{ tip }}</span>
            </div>
            <div class="notice-item">
              <van-icon name="checked" color="#3b82f6" class="mr-2" />
              <span style="color: #ff2719">请认准官方预约渠道，安检全程免费，谨防诈骗！</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部预约按钮 -->
    <div class="submit-bar">
      <!-- 历史预约卡片 -->
      <div class="action-card" @click="goToHistory">
        <div class="action-content">
          <div class="action-left">
            <div class="icon-wrapper">
              <van-icon name="clock-o" color="#7fdbf8" size="24" />
            </div>
            <div class="action-text">
              <div class="action-title">历史预约记录</div>
              <div class="action-desc">查看您的所有安检预约记录</div>
            </div>
          </div>
          <div class="action-right">
            <span class="action-hint">查看</span>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
      <van-button 
        block 
        type="primary" 
        size="large"
        class="custom-button"
        @click="goToForm"
      >
        <template #icon>
          <div class="icon-wrapper-btn">
            <van-icon name="calendar-o" size="24" />
          </div>
        </template>
        <div class="button-title">立即预约</div>
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useMiniStore } from '@/stores/mini'
import {showToast} from 'vant'
const route = useRoute()
const router = useRouter()
const miniStore = useMiniStore()
const openid = route.params.openid
if (openid) {
  miniStore.setOpenid(openid)
} else {
  showToast(`当前用户未登录！`)
}
const tips = [
  '用户两年内已安检过或非民用用户，无需进行安检预约操作。',
  '请确保预约时填写的信息真实有效。',
  '安检期间请家中留人，配合安检人员工作。',
  '当日预约后，未受理前可在历史预约记录中取消。已受理的预约如需变更，请提前联系客服。'
]

const goToForm = () => router.push('/appointment-form')
const goToHistory = () => router.push('/appointment-history')
</script>

<style scoped>
.page-bg {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 6rem;
}

:deep(.van-nav-bar) {
  /*background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%) !important;*/
  background: #00b6ef !important;
}


:deep(.van-nav-bar__title) {
  color: #fff;
  font-size: 18px;
}

.notice-card {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

.notice-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.notice-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-left: 12px;
}

.notice-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  border-radius: 8px;
  font-size: 14px;
  color: #4b5563;
  transition: background-color 0.2s;
}

.notice-item:hover {
  background-color: #f3f4f6;
}

.notice-item.warning {
  background-color: #fffbeb;
  color: #92400e;
}

.notice-item.warning:hover {
  background-color: #fef3c7;
}

.action-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(100, 101, 102, 0.12);
}

.action-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left {
  display: flex;
  align-items: center;
}

.action-text {
  margin-left: 12px;
}

.action-title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 13px;
  color: #6b7280;
}

.action-right {
  display: flex;
  align-items: center;
  color: #9ca3af;
}

.action-hint {
  font-size: 14px;
  margin-right: 4px;
}

.icon-wrapper {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #d4eff8;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-wrapper-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #7fdbf8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-bar {
  padding: 16px;
}

.custom-button {
  /*background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);*/
  background: #00b6ef;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 18px;
  height: 80px;
}

.mr-2 {
  margin-right: 8px;
}

/* 添加客服电话样式 */
:deep(.customer-service-phone) {
  color: #3b82f6;
  font-size: 12px; /* 字体大小 */
  margin-left: 10px; /* 添加一些左边距与温馨提示分开 */
}
.logo-icon {
  height: 28px; /* Adjust height as needed */
}
</style> 