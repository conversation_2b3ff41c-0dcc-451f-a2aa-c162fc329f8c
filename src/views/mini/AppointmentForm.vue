<script setup lang="ts">
import { openApiLogic } from '@af-mobile-client-vue3/services/api/common'
import { showToast } from 'vant'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMiniStore } from '@/stores/mini'
import { encryptMD5 } from '@/utils/encrypt'

const miniStore = useMiniStore()
const router = useRouter()
// 0021111124 张三12
const gasCardNumber = ref('')
const userName = ref('')
const canAppoint = ref(false)
const showDatePicker = ref(false)
const orderDate = ref('')
const orderInfo = ref({
  f_open_id: miniStore.openid,
  f_card_id: '',
  f_user_name: '',
  f_user_phone: '',
  f_user_type: '',
  f_order_start_date: '',
  f_order_end_date: '',
  f_address: '',
  f_user_address: '',
  f_org_id: '',
  f_urban_area: '',
  f_area: '',
  f_street: '',
  f_residential_area: '',
  f_building: '',
  f_unit: '',
  f_floor: '',
  f_room: '',
  f_order_remark: '',
  f_order_source: '小程序',
  f_accept_status: '待受理',
  f_last_inspection: '',
  hasAppointment: false,
  f_is_safe: true,
  countVerify: false,
  f_order_date: '',
})

// 日期相关
const minDate = new Date()
const maxDate = new Date(minDate)
maxDate.setDate(minDate.getDate() + 6)

const form = ref({
  date: '',
  timeSlot: '',
})

const today = new Date()

const currentDate = ref([
  String(today.getFullYear()),
  String(today.getMonth() + 1).padStart(2, '0'),
  String(today.getDate()).padStart(2, '0'),
])

// 时间段配置
const timeSlots = ref([
])

onMounted(() => {
  setTodayAsDefault()
})

function setTodayAsDefault() {
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  const weekDayMap = ['日', '一', '二', '三', '四', '五', '六']
  const weekDay = weekDayMap[today.getDay()]
  form.value.date = `${month}/${day}星期${weekDay}`
  orderDate.value = formatDate(today)
}
// 检查时间段是否已过
function isTimeSlotPassed(slotValue: string) {
  // 1. 获取选中的日期（格式：yyyy-MM-dd）
  const selectedDateStr = orderDate.value
  if (!selectedDateStr)
    return false

  // 2. 构造选中日期的 Date 对象
  const [year, month, day] = selectedDateStr.split('-').map(Number)
  const slotDate = new Date(year, month - 1, day)

  // 3. 获取当前时间
  const now = new Date()

  // 4. 判断选中日期是不是今天
  if (
    slotDate.getFullYear() === now.getFullYear()
    && slotDate.getMonth() === now.getMonth()
    && slotDate.getDate() === now.getDate()
  ) {
    // 5. 如果是今天，解析时间段的起始时间，比如 "9:00-10:00" 取 "9:00"
    const [, endTime] = slotValue.split('-')
    const [hours, minutes] = endTime.split(':').map(Number)

    // 6. 构造今天的“起始时间”Date对象
    const slotTime = new Date()
    slotTime.setHours(hours, minutes, 0, 0)

    // 7. 当前时间超过起始时间，返回true（已过期）
    return now > slotTime
  }

  // 8. 如果不是今天，所有时间段都未过期
  return false
}

// 选择时间段
function selectTimeSlot(slot: any) {
  form.value.timeSlot = ''
  if (slot.remaining <= 0 || isTimeSlotPassed(slot.value)) {
    return
  }
  form.value.timeSlot = slot.value
}

// 查询用户信息
async function searchUserInfo() {
  if (!gasCardNumber.value || !userName.value) {
    showToast({ type: 'fail', message: '请输入燃气卡号和用户姓名' })
    return
  }
  clearUserInfo()
  // 查询用户信息
  await queryUserInfo()

  if (orderInfo.value.f_user_name && orderInfo.value.f_user_name === userName.value) {
    if (orderInfo.value.f_user_type === '非民用') {
      showToast({ type: 'fail', message: '该用户为非民用用户，无需进行安检预约操作' })
      canAppoint.value = false
      return
    }
    await queryUserCheckInfo()
    if (orderInfo.value.f_is_safe) {
      showToast({ type: 'fail', message: '该用户两年内已进行过安检，无需进行安检预约操作' })
      canAppoint.value = false
      return
    }
    await orderSafeCheckVerify()
    if (orderInfo.value.hasAppointment) {
      showToast({ type: 'fail', message: '您当前已有预约，请先取消后再进行新的预约' })
      canAppoint.value = false
      return
    }
    canAppoint.value = true
    await queryCount()
  }
  else {
    showToast({ type: 'fail', message: '燃气卡号或用户姓名不正确' })
    clearUserInfo()
    canAppoint.value = false
  }
}

function queryUserInfo() {
  return new Promise((resolve, reject) => {
    const param = { data: { cardId: gasCardNumber.value } }
    const sign = encryptMD5(`${JSON.stringify(param)}qhgas`)
    const queryParam = { data: { cardId: gasCardNumber.value }, tokenS: sign }
    openApiLogic(queryParam, 'searchUserInfo').then((res) => {
      if (res.userName) {
        // orderInfo.value.f_address = `${res.road}${res.unitName}${res.cusDom}-${res.cusDy}-${res.cusFloor}-${res.cusRoom}`
        orderInfo.value.f_user_address = `${res.road}${res.unitName}${res.cusDom}-${res.cusDy}-${res.cusFloor}-${res.cusRoom}`
        orderInfo.value.f_card_id = gasCardNumber.value
        orderInfo.value.f_user_name = res.userName
        orderInfo.value.f_residential_area = res.unitName
        orderInfo.value.f_street = res.road
        orderInfo.value.f_building = res.cusDom
        orderInfo.value.f_unit = res.cusDy
        orderInfo.value.f_floor = res.cusFloor
        orderInfo.value.f_room = res.cusRoom
        orderInfo.value.f_org_id = res.subunit
        orderInfo.value.f_urban_area = res.subunit
        orderInfo.value.f_area = res.area
        orderInfo.value.f_user_type = res.user_class
      }
      resolve(res)
    }).catch((error) => {
      reject(error)
    })
  })
}

function queryUserCheckInfo() {
  return new Promise((resolve, reject) => {
    const param = { data: { condition: `card_id = '${gasCardNumber.value}' and condition = '正常'` } }
    const sign = encryptMD5(`${JSON.stringify(param)}qhgas`)
    const queryParam = { data: { condition: `card_id = '${gasCardNumber.value}' and condition = '正常'` }, tokenS: sign }
    openApiLogic(queryParam, 'searchUserCheckInfo').then((res) => {
      if (res.length) {
        if (Object.keys(res[0]).length === 0) {
          orderInfo.value.f_is_safe = false
        }
        else {
          if (res[0].is_safe) {
            orderInfo.value.f_is_safe = false
          }
          orderInfo.value.f_last_inspection = res[0].max_departure_time
        }
      }
      else {
        orderInfo.value.f_is_safe = false
        orderInfo.value.f_last_inspection = ''
      }
      resolve(res)
    }).catch((error) => {
      reject(error)
    })
  })
}

// 日期确认
async function onDateConfirm(date) {
  if (orderDate.value !== formatDate(date)) {
    orderDate.value = formatDate(date)
    const weekDayMap = ['日', '一', '二', '三', '四', '五', '六']
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要+1
    const day = String(date.getDate()).padStart(2, '0')
    const weekDay = weekDayMap[date.getDay()]
    form.value.date = `${month}/${day}星期${weekDay}`
    form.value.timeSlot = ''
    await queryCount()
  }
  showDatePicker.value = false
}

function clearUserInfo() {
  orderInfo.value.f_address = ''
  orderInfo.value.f_user_address = ''
  orderInfo.value.f_card_id = ''
  orderInfo.value.f_user_name = ''
  orderInfo.value.f_residential_area = ''
  orderInfo.value.f_street = ''
  orderInfo.value.f_building = ''
  orderInfo.value.f_unit = ''
  orderInfo.value.f_floor = ''
  orderInfo.value.f_room = ''
  orderInfo.value.f_last_inspection = ''
  orderInfo.value.hasAppointment = false
  orderInfo.value.f_org_id = ''
  orderInfo.value.f_urban_area = ''
  orderInfo.value.f_area = ''
  orderInfo.value.f_user_type = ''
  orderInfo.value.f_is_safe = true
  orderInfo.value.f_order_start_date = ''
  orderInfo.value.f_order_end_date = ''
  orderInfo.value.countVerify = false
  orderInfo.value.f_order_date = ''
}

function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以要+1
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

function queryCount() {
  return new Promise((resolve, reject) => {
    if (orderInfo.value.f_org_id) {
      openApiLogic({ f_org_id: orderInfo.value.f_org_id, f_order_date: orderDate.value }, 'queryCountByArea').then((res) => {
        timeSlots.value = res
        resolve(res)
      }).catch((error) => {
        reject(error)
      })
    }
  })
}

function orderSafeCheckVerify() {
  return new Promise((resolve, reject) => {
    if (gasCardNumber.value) {
      openApiLogic({ f_card_id: gasCardNumber.value }, 'orderSafeCheckVerify').then((res) => {
        orderInfo.value.hasAppointment = res.orderVerify === false
        resolve(res)
      }).catch((error) => {
        reject(error)
      })
    }
  })
}
function orderSafeCheckCountVerify() {
  return new Promise((resolve, reject) => {
    if (orderInfo.value.f_org_id && gasCardNumber.value && orderInfo.value.f_order_start_date && orderInfo.value.f_order_end_date) {
      openApiLogic({ f_org_id: orderInfo.value.f_org_id, f_card_id: gasCardNumber.value, f_order_start_date: orderInfo.value.f_order_start_date, f_order_end_date: orderInfo.value.f_order_end_date }, 'orderSafeCheckVerify').then((res) => {
        orderInfo.value.hasAppointment = res.orderVerify === false
        orderInfo.value.countVerify = res.countVerify === true
        resolve(res)
      }).catch((error) => {
        reject(error)
      })
    }
  })
}

// 提交预约
async function onSubmit() {
  if (!canAppoint.value) {
    showToast({ type: 'fail', message: '当前用户无法进行安检预约' })
    return
  }

  if (!form.value.date || !form.value.timeSlot) {
    showToast({ type: 'fail', message: '请选择预约日期和时间段' })
    return
  }
  if (!orderInfo.value.f_address) {
    showToast({ type: 'fail', message: '请填写预约地址！' })
    return
  }
  if (!orderInfo.value.f_user_phone) {
    showToast({ type: 'fail', message: '请填写电话！' })
    return
  }
  // 日期
  const [startTime, endTime] = form.value.timeSlot?.split('-')
  orderInfo.value.f_order_start_date = `${orderDate.value} ${startTime}:00`
  orderInfo.value.f_order_end_date = `${orderDate.value} ${endTime}:00`
  orderInfo.value.f_order_date = `${orderDate.value}`.replaceAll('-', '')
  await orderSafeCheckCountVerify()
  if (orderInfo.value.hasAppointment) {
    showToast({ type: 'fail', message: '您当前已有预约，请先取消后再进行新的预约' })
    canAppoint.value = false
    clearUserInfo()
    return
  }
  if (!orderInfo.value.countVerify) {
    showToast({ type: 'fail', message: '您所在公司当前时间段已约满，请选择其他时间段再进行预约吧！' })
    return
  }
  await saveOrderSafeCheck()
}

function saveOrderSafeCheck() {
  return new Promise((resolve, reject) => {
    openApiLogic(orderInfo.value, 'saveOrderSafeCheck').then((res) => {
      showToast({ type: 'success', message: '安检预约成功！' })
      if (res.value) {
        router.back()
      }
      resolve(res)
    }).catch((error) => {
      showToast({ type: 'success', message: '安检预约失败，请稍后再试！' })
      reject(error)
    })
  })
}
</script>

<template>
  <div class="page-bg">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="安检预约"

      left-arrow placeholder fixed
      :border="false"
      @click-left="router.back()"
    >
      <template #right>
        <img src="@/assets/logo.png" alt="Logo" class="logo-icon">
      </template>
    </van-nav-bar>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 用户信息查询卡片 -->
      <van-cell-group class="custom-card">
        <van-cell title="用户信息查询" class="card-title" />
        <div class="form-fields">
          <van-field
            v-model="gasCardNumber"
            placeholder="请输入燃气卡号"
            clearable
            :rules="[{ required: true, message: '请输入燃气卡号' }]"
          >
            <template #left-icon>
              <van-icon name="card" class="field-icon" />
            </template>
          </van-field>
          <van-field
            v-model="userName"
            placeholder="请输入用户姓名"
            clearable
            :rules="[{ required: true, message: '请输入用户姓名' }]"
          >
            <template #left-icon>
              <van-icon name="contact" class="field-icon" />
            </template>
          </van-field>
          <div class="submit-bar">
            <van-button block type="primary" class="custom-button" @click="searchUserInfo">
              查询
            </van-button>
          </div>
        </div>
      </van-cell-group>

      <!-- 用户信息展示卡片 -->
      <div v-if="orderInfo && canAppoint" class="custom-card user-info-card mt-4">
        <div class="card-title">
          用户信息
        </div>
        <div class="user-info-row">
          <van-icon name="user" class="info-icon" />
          <span class="info-label">姓名</span>
          <span class="info-value">{{ orderInfo.f_user_name }}</span>
        </div>
        <div class="user-info-row">
          <van-icon name="location" class="info-icon" />
          <span class="info-label">地址</span>
          <span class="info-value">{{ orderInfo.f_user_address }}</span>
        </div>
        <div class="user-info-row">
          <van-icon name="clock" class="info-icon" />
          <span class="info-label">上次安检时间</span>
          <span class="info-value">{{ orderInfo.f_last_inspection ? orderInfo.f_last_inspection : '暂无' }}</span>
        </div>
      </div>

      <!-- 预约信息卡片 -->
      <van-cell-group v-if="orderInfo && canAppoint" class="custom-card mt-4">
        <van-cell title="安检预约信息" class="card-title" :label="`每日总预约为${timeSlots.length > 0 ? timeSlots[0].total : 100}名额，剩余${timeSlots.length > 0 ? timeSlots[0].remaining : 0}名额`" />
        <van-field
          v-model="form.date"
          is-link
          readonly
          required
          label="预约日期"
          placeholder="请选择预约日期"
          @click="showDatePicker = true"
        />
        <div v-show="form.date" class="time-slots-grid">
          <div
            v-for="(slot, index) in timeSlots" :key="index"
            class="time-slot-item"
            :class="{
              selected: form.timeSlot === slot.value,
              disabled: slot.remaining <= 0 || isTimeSlotPassed(slot.value),
            }"
            @click="selectTimeSlot(slot)"
          >
            <div class="time-label">
              {{ slot.label }}
            </div>
            <div class="time-remaining" :class="{ 'text-red': isTimeSlotPassed(slot.value) }">
              {{ isTimeSlotPassed(slot.value) ? '已过时间' : '' }}
            </div>
          </div>
        </div>
        <van-field
          v-model="orderInfo.f_address"
          label="预约地址"
          type="textarea"
          required
          placeholder="请输入预约地址"
          rows="2"
          autosize
          :rules="[{ required: true, message: '请输入预约地址' }]"
        />
        <van-field
          v-model="orderInfo.f_user_phone"
          label="电话"
          placeholder="请输入电话"
          clearable
          required
          type="tel"
          :rules="[{ required: true, message: '请输入电话' }]"
        />
        <van-field
          v-model="orderInfo.f_order_remark"
          label="备注"
          type="textarea"
          placeholder="请描述您需要安检的具体情况或特殊要求"
          rows="3"
          autosize
        />
      </van-cell-group>

      <!-- 底部提交按钮 -->
      <div v-if="orderInfo && canAppoint" class="submit-bar">
        <van-button block type="primary" size="large" class="custom-button" @click="onSubmit">
          <template #icon>
            <van-icon name="calendar-o" />
          </template>
          提交安检预约
        </van-button>
      </div>
    </div>

    <van-calendar
      v-model:show="showDatePicker"
      color="#00b6ef"
      :min-date="minDate"
      :max-date="maxDate"
      :default-date="currentDate as any"
      title="选择预约日期"
      @confirm="onDateConfirm"
    />

    <!-- 日期选择弹窗 -->
    <!--    <van-popup v-model:show="showDatePicker" position="bottom" round> -->
    <!--      <van-date-picker -->
    <!--        :min-date="minDate" -->
    <!--        :max-date="maxDate" -->
    <!--        :default-value="currentDate" -->
    <!--        @confirm="onDateConfirm" -->
    <!--        @cancel="showDatePicker = false" -->
    <!--        title="选择预约日期" -->
    <!--      /> -->
    <!--    </van-popup> -->
  </div>
</template>

<style scoped>
.page-bg {
  min-height: 100vh;
  background: #f7f8fa;
  width: 100vw;
  padding-bottom: 6rem;
}

.content-area {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

:deep(.van-nav-bar) {
  /*background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%) !important;*/
  background: #00b6ef !important;
}

:deep(.van-nav-bar__title),
:deep(.van-nav-bar .van-icon) {
  color: #fff;
  font-size: 18px;
}

.custom-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.field-icon {
  font-size: 20px;
  color: #666;
}

.custom-button {
  /*background: linear-gradient(90deg, #6366f1 0%, #7c3aed 100%);*/
  background: #00b6ef;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  width: 100%;
}

.submit-bar {
  padding: 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 80px;
}

:deep(.van-picker__title) {
  font-size: 16px;
  font-weight: 500;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}

.time-slot-item {
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.time-slot-item.selected {
  border-color: #1989fa;
  background-color: #e8f3ff;
}

.time-slot-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.time-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.time-remaining {
  font-size: 12px;
  color: #969799;
}

.time-remaining.text-red {
  color: #ee0a24;
}

.user-info-card {
  padding: 16px;
}

.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.user-info-row:last-child {
  margin-bottom: 0;
}

.info-icon {
  margin-right: 8px;
  color: #7fdbf8;
  font-size: 18px;
}

.info-label {
  width: 90px;
  color: #888;
  font-size: 14px;
  margin-right: 8px;
  flex-shrink: 0;
}

.info-value {
  color: #222;
  font-size: 15px;
  flex: 1;
  word-break: break-all;
}
.logo-icon {
  height: 28px; /* Adjust height as needed */
}
</style>
