<template>
  <div class="page-bg">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="预约历史"
      left-arrow
      fixed
      placeholder
      @click-left="goBack"
      :border="false"
    >
      <template #right>
        <img src="@/assets/logo.png" alt="Logo" class="logo-icon">
      </template>
    </van-nav-bar>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 筛选菜单 -->
      <div class="filter-menu">
        <van-dropdown-menu>
          <van-dropdown-item v-model="filter" :options="filterOptions" />
        </van-dropdown-menu>
      </div>

      <!-- 预约列表 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-for="item in filteredAppointments" :key="item.id" class="appointment-card">
          <div class="appointment-header">
            <div class="header-left">
              <span class="header-tip" v-show="item.f_accept_status === '待安检'">安检员{{ item.f_accept_user_name }}已接单，电话: {{ item.f_accept_user_phone || '暂无' }}，将为您入户安检，请耐心等待，注意电话接听。</span>
              <h3 class="service-type">燃气安检
                <van-tag style="margin-left: 60%;margin-top: -5px" :type="statusTagType(item.f_accept_status)" round>
                  {{ item.f_accept_status }}
                </van-tag>
              </h3>
              <p class="appointment-time">{{ formatDateTimeRange(item.f_order_start_date,item.f_order_end_date) }}</p>
            </div>
          </div>

          <div class="appointment-info">
            <div class="info-item">
              <van-icon name="card" class="mr-2" />
              {{ item.f_card_id }}
            </div>
            <div class="info-item">
              <van-icon name="user" class="mr-2" />
              {{ item.f_user_name }}
            </div>
            <div class="info-item">
              <van-icon name="phone" class="mr-2" />
              {{ item.f_user_phone }}
            </div>
            <div class="info-item">
              <van-icon name="location" class="mr-2" />
              {{ item.f_address }}
            </div>

            <template v-if="item.f_accept_status === '已安检'">
              <div class="inspector-section">
                <p class="section-title">安检员信息</p>
                <div class="info-item">
                  <van-icon name="manager" class="mr-2" />
                  {{ item.f_accept_user_name }}
                </div>
                <div class="info-item">
                  <van-icon name="card" class="mr-2" />
                  {{ item.f_accept_user_id }}
                </div>
                <div class="info-item">
                  <van-icon name="phone" class="mr-2" />
                  {{ item.f_accept_user_phone }}
                </div>
              </div>
            </template>

            <template v-if="item.f_accept_status === '已安检'">
              <div class="inspection-section">
                <p class="section-title">安检结果</p>
                <div class="result-status" :class="item.f_check_result === '正常' ? 'text-success' : 'text-warning'">
                  <van-icon name="passed" class="mr-2" />
                  {{ item.f_check_result }}
                </div>
                <div v-if="item.f_problem_desc" class="result-issues">
                  <van-icon name="info" class="mr-2" />
                  {{ item.f_problem_desc }}
                </div>
                <div v-if="item.f_remark" class="result-notes">
                  <van-icon name="notes" class="mr-2" />
                  {{ item.f_remark }}
                </div>
              </div>
            </template>
          </div>

          <div v-if="item.f_accept_status === '待受理' || item.f_accept_status === '待安检'" class="action-buttons">
            <van-button 
              plain 
              type="danger" 
              size="small" 
              icon="close"
              style="margin-right: 20px"
              @click="openCancel(item)"
            >
              取消预约
            </van-button>
            <van-button
              plain
              type="warning"
              size="small"
              icon="replay"
              @click="openChange(item)"
            >
              改约
            </van-button>
          </div>
        </div>
      </van-list>
    </div>

    <!-- 取消预约弹窗 -->
    <van-dialog
      v-model:show="showCancelModal"
      title="取消预约"
      show-cancel-button
      confirm-button-text="确认取消"
      cancel-button-text="返回"
      :before-close="beforeDialogClose"
      @confirm="confirmCancel"
    >
      <div class="cancel-dialog-content">
        <van-radio-group v-model="selectedReason">
          <van-cell-group inset>
            <van-cell
              v-for="reason in cancelReasons"
              :key="reason"
              :title="reason"
              clickable
              @click="selectedReason = reason"
            >
              <template #right-icon>
                <van-radio :name="reason" />
              </template>
            </van-cell>
            <van-cell title="其他原因" clickable @click="selectedReason = 'custom'">
              <template #right-icon>
                <van-radio name="custom" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>

        <van-field
          v-if="selectedReason === 'custom'"
          v-model="customReason"
          type="textarea"
          placeholder="请详细说明取消原因"
          rows="3"
          autosize
          class="mt-4"
        />
      </div>
    </van-dialog>
    <van-dialog
      v-model:show="showChangeModal"
      title="修改预约时段"
      show-cancel-button
      confirm-button-text="确认修改"
      cancel-button-text="返回"
      :before-close="beforeDialogChangeClose"
      @confirm="confirmChange"
    >
      <div class="cancel-dialog-content">
        <van-cell-group>
          <van-cell title=""  class="card-title" :label="`每日总预约为${timeSlots.length > 0 ? timeSlots[0].total : 100}名额，剩余${timeSlots.length > 0 ? timeSlots[0].remaining : 0}名额`"/>
          <van-cell title=""  class="card-title" :label="`原预约时间：${formatDateTimeRange(selectedAppointment.f_order_start_date,selectedAppointment.f_order_end_date)}`"/>
          <van-field
            v-model="form.date"
            is-link
            readonly
            required
            label="预约日期"
            placeholder="请选择预约日期"
            @click="showDatePicker = true"
          />
          <div class="time-slots-grid" v-show="form.date">
            <div v-for="(slot, index) in timeSlots" :key="index"
                 class="time-slot-item"
                 :class="{
                 'selected': form.timeSlot === slot.value,
                 'disabled': slot.remaining <= 0 || isTimeSlotPassed(slot.value)
               }"
                 @click.stop="selectTimeSlot(slot)">
              <div class="time-label">{{ slot.label }}</div>
              <div class="time-remaining" :class="{ 'text-red': isTimeSlotPassed(slot.value) }">
                {{ isTimeSlotPassed(slot.value) ? '已过时间' : '' }}
              </div>
            </div>
          </div>
        </van-cell-group>
      </div>
    </van-dialog>
    <van-calendar
      v-model:show="showDatePicker"
      color="#00b6ef"
      :min-date="minDate"
      :max-date="maxDate"
      :default-date="currentDate"
      @confirm="onDateConfirm"
      title="选择预约日期"
    />
  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {showToast} from 'vant'
import {useMiniStore} from '@/stores/mini'
import {openApiLogic} from '@af-mobile-client-vue3/services/api/common'

const miniStore = useMiniStore()
const router = useRouter()
const goBack = () => router.back()
const openid = miniStore.openid
console.log('openid', openid)
// 列表加载状态
const loading = ref(false)
const finished = ref(true)
const onLoad = () => {
  loading.value = false
}

// 筛选相关
const filter = ref('')
const filterOptions = [
  { text: '全部预约', value: '' },
  { text: '待受理', value: '待受理' },
  { text: '待安检', value: '待安检' },
  { text: '已安检', value: '已安检' },
  { text: '已作废', value: '已作废' }
]

const filteredAppointments = ref([])

// 取消预约相关
const showCancelModal = ref(false)
const showChangeModal = ref(false)
const selectedAppointment = ref<any>(null)
const selectedReason = ref('')
const customReason = ref('')
const cancelReasons = [
  '时间安排有变',
  '已自行解决问题',
  '选择其他时间',
  '地址信息有误',
  '重复预约'
]

const form = ref({
  date: '',
  timeSlot: ''
})
const showDatePicker = ref(false)
const timeSlots = ref([
])
const orderDate = ref('')
// 日期相关
const currentDate = ref(new Date())
const minDate = computed(() => {
  return new Date()
})

const maxDate = computed(() => {
  const date = new Date()
  date.setDate(date.getDate() + 6)
  return date
})

onMounted(()=>{
  queryOrderList()
})

watch(filter, () => {
  queryOrderList()
})

function formatDate (date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要+1
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}
function queryCount () {
  return new Promise((resolve, reject) => {
    if (selectedAppointment.value.f_org_id) {
      openApiLogic({f_org_id: selectedAppointment.value.f_org_id, f_order_date: orderDate.value}, 'queryCountByArea').then(res => {
        timeSlots.value = res
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    }
  })
}
const onDateConfirm = async (date: Date) => {
  if (orderDate.value !== formatDate(date)) {
    orderDate.value = formatDate(date)
    const weekDayMap = ['日', '一', '二', '三', '四', '五', '六']
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要+1
    const day = String(date.getDate()).padStart(2, '0')
    const weekDay = weekDayMap[date.getDay()]
    form.value.date = `${month}/${day}星期${weekDay}`
    form.value.timeSlot = ''
    await queryCount()
  }
  currentDate.value = date
  showDatePicker.value = false
}
const isTimeSlotPassed = (slotValue: string) => {
  // 1. 获取选中的日期（格式：yyyy-MM-dd）
  const selectedDateStr = orderDate.value;
  if (!selectedDateStr) return false;

  // 2. 构造选中日期的 Date 对象
  const [year, month, day] = selectedDateStr.split('-').map(Number);
  const slotDate = new Date(year, month - 1, day);

  // 3. 获取当前时间
  const now = new Date();

  // 4. 判断选中日期是不是今天
  if (
    slotDate.getFullYear() === now.getFullYear() &&
    slotDate.getMonth() === now.getMonth() &&
    slotDate.getDate() === now.getDate()
  ) {
    // 5. 如果是今天，解析时间段的起始时间，比如 "9:00-10:00" 取 "9:00"
    const [, endTime] = slotValue.split('-');
    const [hours, minutes] = endTime.split(':').map(Number);

    // 6. 构造今天的"起始时间"Date对象
    const slotTime = new Date();
    slotTime.setHours(hours, minutes, 0, 0);

    // 7. 当前时间超过起始时间，返回true（已过期）
    return now > slotTime;
  }

  // 8. 如果不是今天，所有时间段都未过期
  return false;
};
const selectTimeSlot = (slot: any) => {
  form.value.timeSlot = ''
  if (slot.remaining <= 0 || isTimeSlotPassed(slot.value)) {
    return
  }
  form.value.timeSlot = slot.value
}
function queryOrderList () {
  return new Promise((resolve, reject) => {
    let queryParam = {
      f_open_id: openid,
      f_accept_status: filter.value
    }
    openApiLogic(queryParam, 'getOrderCheckList').then(res => {
      filteredAppointments.value = res
      resolve(res)
    }).catch(error => {
      reject(error)
    })
  })
}
function formatDateTimeRange(startDate, endDate) {
  // 解析日期
  const start = new Date(startDate);
  const end = new Date(endDate);

  // 提取年月日
  const year = start.getFullYear();
  const month = String(start.getMonth() + 1).padStart(2, '0');
  const day = String(start.getDate()).padStart(2, '0');

  // 判断上午/下午
  const ampm = start.getHours() < 12 ? '上午' : '下午';

  // 提取时间（12小时制）
  const startHours = String(start.getHours() % 12 || 12).padStart(2, '0');
  const startMinutes = String(start.getMinutes()).padStart(2, '0');

  const endHours = String(end.getHours() % 12 || 12).padStart(2, '0');
  const endMinutes = String(end.getMinutes()).padStart(2, '0');

  // 组合结果
  return `${year}-${month}-${day} ${ampm}(${startHours}:${startMinutes} - ${endHours}:${endMinutes})`;
}

// 打开取消预约弹窗
const openCancel = (appointment: any) => {
  selectedAppointment.value = appointment
  selectedReason.value = ''
  customReason.value = ''
  showCancelModal.value = true
}
const openChange = (appointment: any) => {
  selectedAppointment.value = appointment
  const orderDate = new Date(appointment.f_order_start_date);
  const now = new Date();
// 只取年月日部分
  const orderYMD = new Date(orderDate.getFullYear(), orderDate.getMonth(), orderDate.getDate());
  const nowYMD = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  let finalDate;
  if (orderYMD < nowYMD) {
    finalDate = nowYMD;
  } else {
    finalDate = orderYMD;
  }
  currentDate.value = new Date(finalDate.getFullYear(), finalDate.getMonth() + 1, finalDate.getDate())

  onDateConfirm(finalDate);

  showChangeModal.value = true
}

const statusTagType = (status: string) => {
  switch (status) {
    case '待受理':
      return 'warning'
    case '待安检':
      return 'primary'
    case '已安检':
      return 'success'
    case '已作废':
      return 'danger'
    default:
      return 'warning'
  }
}

// 关闭弹窗前确认
const beforeDialogClose = (action: string) => {
  if (action === 'confirm') {
    if (!selectedReason.value) {
      showToast('请选择取消原因')
      return false
    }
    if (selectedReason.value === 'custom' && !customReason.value) {
      showToast('请填写取消原因')
      return false
    }
    return true
  }
  return true
}

const beforeDialogChangeClose = (action: string) => {
  if (action === 'confirm') {
    if (!form.value.date || !form.value.timeSlot) {
      showToast({type: 'fail', message: '请选择预约日期和时间段'})
      return false
    }
    const [startTime, endTime] = form.value.timeSlot?.split('-')
    const f_order_change_start_date = `${orderDate.value} ${startTime}:00`
    const f_order_change_end_date = `${orderDate.value} ${endTime}:00`
    if (selectedAppointment.value.f_order_start_date === f_order_change_start_date || selectedAppointment.value.f_order_end_date === f_order_change_end_date) {
      showToast({type: 'fail', message: '与原预约时段一致请更换预约时段'})
      return false
    }
    return true
  }
  return true
}
function orderSafeCheckCountVerify () {
  return new Promise((resolve, reject) => {
    openApiLogic(
      {f_org_id: selectedAppointment.value.f_org_id, f_card_id: selectedAppointment.value.f_card_id, f_order_start_date: selectedAppointment.value.f_order_change_start_date, f_order_end_date: selectedAppointment.value.f_order_change_end_date},
      'orderSafeCheckVerify'
    )
      .then((res) => {
        selectedAppointment.value.countVerify = res.countVerify === true
        resolve(res)
      })
      .catch(reject)
  })
}
// 确认取消预约
const confirmCancel = async () => {
  if (!selectedReason.value) {
    showToast('请选择取消原因')
    return
  }
  if (selectedReason.value === 'custom' && !customReason.value) {
    showToast('请填写取消原因')
    return
  }

  await cancelOrder()
  await queryOrderList()
  showCancelModal.value = false
}
const confirmChange = async () => {
  if (!form.value.date || !form.value.timeSlot) {
    return
  }
  const [startTime, endTime] = form.value.timeSlot?.split('-')
  selectedAppointment.value.f_order_change_start_date = `${orderDate.value} ${startTime}:00`
  selectedAppointment.value.f_order_change_end_date = `${orderDate.value} ${endTime}:00`
  if (selectedAppointment.value.f_order_start_date === selectedAppointment.value.f_order_change_start_date || selectedAppointment.value.f_order_end_date === selectedAppointment.value.f_order_change_end_date) {
    return
  }
  await orderSafeCheckCountVerify()
  if (!selectedAppointment.value.countVerify) {
    showToast({ type: 'fail', message: '您所选日期对应公司已约满，请选择其他日期再进行预约吧！' })
    return
  }
  await changeOrder()
  await queryOrderList()
}
function cancelOrder () {
  return new Promise((resolve, reject) => {
    if (selectedAppointment.value) {
      openApiLogic({id: selectedAppointment.value.id}, 'cancelOrderCheck').then(res => {
        console.log('res', res)
        showToast('预约已取消')
        resolve(res)
      }).catch(error => {
        showToast('预约取消失败，请稍后再试')
        reject(error)
      })
    }
  })
}
function changeOrder () {
  return new Promise((resolve, reject) => {
    openApiLogic(selectedAppointment.value, 'changeOrderCheck').then(res => {
      if (res.changeSuccess) {
        showToast({type: 'success', message: '预约修改成功！'})
      } else {
        showToast({type: 'success', message: res.msg})
      }
      showChangeModal.value = false
      resolve(res)
    }).catch(error => {
      showChangeModal.value = false
      showToast({type: 'success', message: '预约修改失败，请稍后再试！'})
      reject(error)
    })
  })
}

// 组件卸载时的清理
onUnmounted(() => {
  // 清理响应式引用
  currentDate.value = null
  showDatePicker.value = false
})
</script>

<style scoped>
.page-bg {
  min-height: 100vh;
  background-color: #f7f8fa;
  width: 100vw;
}
:deep(.van-nav-bar) {
  /*background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);*/
  background: #00b6ef;
}
:deep(.van-nav-bar__title),
:deep(.van-nav-bar .van-icon) {
  color: #fff;
  font-size: 18px;
}
.content-area {
  padding: 16px;
  box-sizing: border-box;
}

.filter-menu {
  margin-bottom: 16px;
}

.appointment-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
  box-sizing: border-box;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.header-left {
  flex: 1;
}

.service-type {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

.appointment-time {
  font-size: 14px;
  color: #969799;
}

.appointment-info {
  font-size: 14px;
  color: #646566;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.inspector-section,
.inspection-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 8px;
}

.result-status {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.result-status.text-success {
  color: #07c160;
}

.result-status.text-warning {
  color: #ff976a;
}

.result-issues,
.result-notes {
  display: flex;
  align-items: flex-start;
  margin-top: 8px;
  color: #969799;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.cancel-dialog-content {
  padding: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.mt-4 {
  margin-top: 16px;
}
.logo-icon {
  height: 28px; /* Adjust height as needed */
}
.header-tip {
  font-size: 14px;
  color: #00b6ef;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
}
.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5px;
  padding: 8px;
}

.time-slot-item {
  border: 1px solid #ebedf0;
  border-radius: 4px;
  padding: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}
.time-slot-item.selected {
  border-color: #1989fa;
  background-color: #e8f3ff;
}

.time-slot-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.time-label {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.time-remaining {
  font-size: 10px;
  color: #969799;
}

.time-remaining.text-red {
  color: #ee0a24;
}
</style> 