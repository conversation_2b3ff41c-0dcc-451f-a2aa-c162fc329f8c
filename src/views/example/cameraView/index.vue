<script setup lang="ts">
import { mobileUtil } from '@/views/example/example1/mobileUtil'
import { computed } from 'vue'
const emit = defineEmits(['change'])
const images = computed(()=>{
  return {
    images: fileList.value.map((item)=>{
      return item.content
    }),
    loop: false
  }
})
const p=ref('')
const openCamera = (file) => {
  // 调用手机本地拍照
  mobileUtil.execute({
    funcName:'takePicture',
    param: {},
    callbackFunc: (result) => {
      console.log('result.length===', result)
      fileList.value.push({content:`data:image/png;base64,${result.content}`,filePath:result.filePath})
      p.value=`file:/${result.filePath}`
      console.log('p===', p.value)
      emit('change',fileList.value.map((item)=>{
        return item.filePath
      }))
    }
  });
}
const fileList = ref([]);
</script>

<template>
  <van-uploader :preview-size="120" v-model="fileList" max-count="10" @click-upload="openCamera" :preview-options="images" />
  <img :src="p"/>
</template>

<style scoped lang="less">

</style>
