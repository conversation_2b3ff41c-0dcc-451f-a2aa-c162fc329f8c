<script setup lang="ts">
const active = ref(0)
const list = ref([])
const loading = ref(false)
const finished = ref(false)

function onLoad() {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  setTimeout(() => {
    for (let i = 0; i < 10; i++)
      list.value.push(list.value.length + 1)

    // 加载状态结束
    loading.value = false

    // 数据全部加载完成
    if (list.value.length >= 20)
      finished.value = true
  }, 500)
}
</script>

<template>
  <main class="chat_main h-full w-full">
    <div class="header">
      <van-row class="header_row" justify="space-between">
        <van-col class="header_title_col">
          <p>消息</p>
        </van-col>
        <van-col>
          <van-icon class="header_search_icon" name="search" />
        </van-col>
      </van-row>
    </div>
    <div class="content">
      <van-tabs v-model:active="active">
        <van-divider />
        <van-tab title="消息">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <van-swipe-cell v-for="item in list" :key="item" :title="item">
              <div class="message_card">
                <van-row :gutter="12" class="message_row">
                  <van-col>
                    <img alt="header_img" class="header_img" src="https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg">
                  </van-col>
                  <van-col class="message_card_col">
                    <div class="message_card_main">
                      <van-row justify="space-between">
                        <van-col>
                          <p class="message_sender_name">
                            测试人
                          </p>
                        </van-col>
                        <van-col>
                          <span class="message_sender_time">18分钟前</span>
                        </van-col>
                      </van-row>
                      <p class="message_content">
                        <span>轻舟已过万重山</span>
                      </p>
                    </div>
                    <van-divider class="content_divider" />
                  </van-col>
                </van-row>
              </div>
              <template #right>
                <van-button square text="删除" type="danger" class="delete-button" />
              </template>
            </van-swipe-cell>
          </van-list>
        </van-tab>
        <van-tab title="同事">
          内容 1
        </van-tab>
        <van-tab title="部门">
          内容 1
        </van-tab>
      </van-tabs>
    </div>
  </main>
</template>

<style scoped lang="less">
.chat_main {
  padding: var(--base-interval-1);
  .header {
    p {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
    }
    .header_row {
      align-items: center;
      .header_title_col {
        flex-grow: 1;
      }
      .header_search_icon {
        font-size: 26px;
      }
    }
  }
  .content {
    .message_card {
      .message_row {
        .header_img {
          width: 40px;
          height: 40px;
          border-radius: 5px;
          vertical-align: middle;
        }
        .message_card_col {
          flex-grow: 1;
          .message_sender_name {
            margin: 0 0 3px;
            font-size: 16px;
            font-weight: bold;
          }
          .message_sender_time {
            font-size: 12px;
            color: #999;
          }
          .message_content {
            margin: 0;
            font-size: 12px;
            color: #666;
          }
        }
      }
      .content_divider {
        margin: 12px 0;
      }
    }
  }
}
</style>
