<script setup lang="ts">
import { getFunction } from '@af-mobile-client-vue3/utils/common'
import { useRoute, useRouter } from 'vue-router'

const curFunction = getFunction(useRoute().name as string)?.children

const router = useRouter()
</script>

<template>
  <main class="chat_main h-full w-full">
    <van-notice-bar
      left-icon="volume-o"
      text="欢迎来到智慧燃气移动端示例页面"
    />
    <van-cell>
      <template #title>
        <span>现在是什么情况？</span>
      </template>
      服务启动成功
    </van-cell>
    <van-cell>
      <template #title>
        <span>这是什么页面？</span>
      </template>
      这是欢迎页
    </van-cell>
    <van-cell>
      <template #title>
        <span>接下来干什么？</span>
      </template>
      写你自己的业务呀
    </van-cell>
    <van-button v-for="(item, index) in curFunction" :key="index" type="primary" @click="router.push({ name: item.link, params: { id: 123 } })">
      {{ item.name }}
    </van-button>
  </main>
</template>

<style scoped lang="less">

</style>
