<script step lang="ts">

</script>
<template>
  <div id="SafeCheckUserInfo">
    <van-form>
      <van-cell-group>
        <van-field
          name="用户编号"
          label="用户编号"
          placeholder="用户编号"
        />
        <van-field
          name="用户名称"
          label="用户名称"
          placeholder="用户名称"
        />
        <van-field
          name="用户类型"
          label="用户类型"
          placeholder="用户类型"
        />
        <van-field
          name="用户电话"
          label="用户电话"
          placeholder="用户电话"
        />
        <van-field
          name="用户地址"
          label="用户地址"
          placeholder="用户地址"
        />
      </van-cell-group>
    </van-form>
  </div>
</template>
<style scoped>

</style>