interface Param {
  funcName:string,//注册列表中的对应函数的key
  param:any,//入参json格式
  callbackFunc: Function,//回调函数
  callBackMethodName?: string//随机回调函数名字-不用传
}
// js端调用flutter中工具函数
/*
* 例：mobileUtil.execute({
    funcName:'getLocationResult',
    param: {a: 1},
    callbackFunc: (result) => {
      console.log('回调了test111', JSON.stringify(result))
      message.value = JSON.stringify(result)
      return 222
    }
  });
* */
export class mobileUtil{
  // 执行flutter端函数
  static execute (locationParam: Param):any{
    locationParam.callBackMethodName="mobile_func_" + Math.random().toString(36).substring(7)
    window[locationParam.callBackMethodName]=locationParam.callbackFunc
    window[locationParam.funcName].postMessage(JSON.stringify(locationParam))
  }
}
