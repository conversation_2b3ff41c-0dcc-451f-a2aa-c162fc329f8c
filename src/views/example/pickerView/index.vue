<script setup lang="ts">
interface Props {
  columns: { label: string, value: string }[]
  placeholder?: string
  columnsFieldNames?: { text: string, value: string, children: string }
}
const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  placeholder: '请选择',
  columnsFieldNames: () => { return { text: 'label', value: 'value', children: 'children' } },
})
const showPicker = ref(false)
const selectValue = ref(undefined)
function onConfirm({ selectedOptions }) {
  console.log(selectedOptions)
  showPicker.value = false
  selectValue.value = selectedOptions[0].value
}
</script>

<template>
  <van-field
    v-model="selectValue"
    is-link
    readonly
    :placeholder="placeholder"
    @click="showPicker = true"
  />
  <van-popup v-model:show="showPicker" round position="bottom">
    <van-picker
      :columns="columns"
      :columns-field-names="columnsFieldNames"
      @cancel="showPicker = false"
      @confirm="onConfirm"
    />
  </van-popup>
</template>
