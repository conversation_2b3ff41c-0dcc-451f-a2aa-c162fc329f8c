<script setup lang="ts">
interface Props {
  title: string
  label: string
  placeholder?: string
}
const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  placeholder: '请选择',
})
const showPicker = ref(false)
const selectValue = ref(undefined)
console.log('selectValue.value', selectValue.value)
const currentDate = ref([])
const currentTime = ref([])
watch(selectValue, () => {
  currentDate.value = selectValue.value ? selectValue.value.split(' ')[0].split('-') : []
  currentTime.value = selectValue.value ? selectValue.value.split(' ')[1].split(':') : []
}, { immediate: true })
function onConfirm(value) {
  console.log('value====', value)
  showPicker.value = false
  selectValue.value = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
}
</script>

<template>
  <van-field
    v-model="selectValue"
    is-link
    readonly
    :label="label"
    :placeholder="placeholder"
    @click="showPicker = true"
  />
  <van-popup v-model:show="showPicker" round position="bottom">
    <van-picker-group
      :title="title"
      :tabs="['选择日期', '选择时间']"
      @confirm="onConfirm"
      @cancel="showPicker = false"
    >
      <van-date-picker
        v-model="currentDate"
      />
      <van-time-picker v-model="currentTime" :columns-type="['hour', 'minute', 'second']" />
    </van-picker-group>
  </van-popup>
</template>
