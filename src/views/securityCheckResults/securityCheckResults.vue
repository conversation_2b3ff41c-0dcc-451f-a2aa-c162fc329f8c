<script setup lang="ts">
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { runLogic } from '@/services/api/common'

// 访问配置名
const configName = ref('crud_viewing_security_check_results')
// 访问服务名
const serviceName = ref('af-safecheck')

// 定义接口
interface CardData {
  title: string
  number: number | string
  color: string
}

// 创建响应式数组
const myArray = reactive<CardData[]>([
  { title: '已安检总数', number: 100, color: 'rgba(14,144,255)' },
  { title: '正常入户数', number: 96, color: 'rgba(48,182,32)' },
  { title: '未入户数', number: 96, color: 'rgba(176,134,15)' },
  { title: '入户率', number: '60%', color: 'rgba(255,147,37)' },
])

async function getData() {
  const res = await runLogic('securityCheckResultsLOGIC', {}, 'af-safecheck')
  myArray[0].number = ((res as unknown) as { securityCheckNumber: { num: number }[] }).securityCheckNumber[0]?.num || 0
  myArray[1].number = ((res as unknown) as { numberOfHouseholds: { num: number }[] }).numberOfHouseholds[0]?.num || 0
  myArray[2].number = ((res as unknown) as { numberOfUploads: { num: number }[] }).numberOfUploads[0]?.num || 0
  myArray[3].number = ((res as unknown) as { thePenetrationRate: { result: string }[] }).thePenetrationRate[0]?.result || '0%'
}

onMounted(() => {
  getData()
})
</script>

<template>
  <NormalDataLayout id="123" title="安检结果">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :service-name="serviceName">
        <template #search-after>
          <div class="custom-content">
            <div class="core">
              <van-row class="core_row">
                <van-col v-for="item in myArray" :key="item.title">
                  <p class="core_value" :style="{ color: item.color }">
                    {{ item.number }}
                  </p>
                  <p class="core_title">
                    {{ item.title }}
                  </p>
                </van-col>
              </van-row>
            </div>
          </div>
        </template>
      </XCellList>
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
.custom-content {
  padding: 0 13px;
  margin-top: -10px;
  .core {
    background-color: #fff;
    border-radius: 8px;
    padding: 10px;
    .core_row {
      display: flex;
      justify-content: space-around;
      text-align: center;

      .van-col {
        flex: 1;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          height: 70%;
          width: 1px;
          background-color: #ebedf0;
        }

        .core_value {
          font-size: 20px;
          font-weight: bolder;
          margin: 0 0 4px;
        }

        .core_title {
          font-size: 13px;
          color: rgba(125,125,125);
          margin: 0;
        }
      }
    }
  }
}
</style>
