<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 访问配置名
const configName = ref('temporarySecurityCheckCRUD')
// 访问服务名
const serviceName = ref('af-safecheck')

const router = useRouter()
function startService() {
  router.push({
    name: 'SecurityCertificate',
    // name: 'test',
  })
}
</script>

<template>
  <div class="main">
    <!-- 调用XCellList组件渲染列表 -->
    <XCellList
      :config-name="configName"
      :service-name="serviceName"
      @start-service="startService"
    />
  </div>
</template>

<style scoped lang="less">
.main {
  padding: var(--base-interval-1);

  :deep(.van-search) {
    padding: 0;
  }
}
</style>
