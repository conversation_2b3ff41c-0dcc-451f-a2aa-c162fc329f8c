<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import XDetail from '@/views/core/security-check-task/XDetail.vue'

// 访问路由
const route = useRoute()
// 获取参数
const userinfoCode = {
  queryParamsName: 'GetSafeCheckTaskCRUD',
  pageNo: 1,
  pageSize: 20,
  conditionParams: {
    cpi_f_userinfo_code: route.query.id,
  },
}
const userApi = route.query.api as unknown as unknown as string
// 设置状态
const loading = ref(false)
// 解析父组件接收到子组件传递的数据
const userCode = ref('')
const userName = ref('')
const userPhone = ref('')
const userAddress = ref('')
function detailResult(result) {
  userCode.value = result.data[0].cpi_f_userinfo_code
  userName.value = result.data[0].cpi_f_user_name
  userPhone.value = result.data[0].cpi_f_user_phone
  userAddress.value = result.data[0].cpi_f_address
}
</script>

<template>
  <div id="XDetail">
    <!-- 调用组件XDetail，渲染详情 -->
    <XDetail
      :api="userApi"
      :params="userinfoCode"
      :loading="loading"
      @detail-result="detailResult"
    >
      <template #header>
        <p>ceshi </p>
      </template>
      <!-- 调用插槽 -->
      <template #body>
        <van-tab title="1">
          <van-button type="primary">
            {{ userCode }}
          </van-button>
          <van-button type="success">
            {{ userName }}
          </van-button>
          <van-button type="default">
            {{ userPhone }}
          </van-button>
          <van-button type="warning">
            {{ userAddress }}
          </van-button>
        </van-tab>
        <van-tab title="22">
          <van-button type="primary">
            {{ userCode }}
          </van-button>
          <van-button type="success">
            {{ userName }}
          </van-button>
          <van-button type="default">
            {{ userPhone }}
          </van-button>
          <van-button type="warning">
            {{ userAddress }}
          </van-button>
        </van-tab>
        <van-tab title="33">
          <van-button type="primary">
            {{ userCode }}
          </van-button>
          <van-button type="success">
            {{ userName }}
          </van-button>
          <van-button type="default">
            {{ userPhone }}
          </van-button>
          <van-button type="warning">
            {{ userAddress }}
          </van-button>
        </van-tab>
      </template>
    </XDetail>
  </div>
</template>

<style scoped lang="less">
.detail-footer {
  margin-top: 1vh;
  padding: 1vh 2vh 1vh 2vh;
  background-color: white;

  display: flex;
  flex-direction: column;
}
</style>
