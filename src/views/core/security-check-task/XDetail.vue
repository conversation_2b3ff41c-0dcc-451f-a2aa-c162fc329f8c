<script setup lang="ts">
import { post } from '@af-mobile-client-vue3/services/restTools'
import {
  Row as VanRow,
  Skeleton as VanSkeleton,
} from 'vant'
import { defineEmits, ref } from 'vue'

// 提取传递过来的属性
const { api, params, loading } = defineProps<{
  api?: string
  params?: object
  loading?: boolean
}>()
// 子组件向父组件传递结果数据
const emit = defineEmits<{
  detailResult: [result: any]
}>()
// 设置初始查询状态
const loadDetails = ref(false)
// 设置结果
const details = ref({})
// 根据api和id发送请求，查询结果
function getDetails() {
  if (api && params) {
    loadDetails.value = true
    post(api, params).then((res) => {
      details.value = res
      emit('detailResult', res)
    }, (err) => {
      console.error(err)
    }).finally(() => {
      loadDetails.value = false
    })
  }
}

onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="main">
    <VanSkeleton title :row="20" :loading="loading" class="skeleton-box">
      <!-- 标题区域 -->
      <VanRow v-show="'header' in $slots" class="detail-header">
        <slot name="header" :header-details="details" />
      </VanRow>
      <!-- 详情区域 -->
      <VanRow v-show="'body' in $slots" class="detail-body">
        <van-tabs>
          <slot name="body" :body-details="details" />
        </van-tabs>
      </VanRow>
      <!-- 底部区域 -->
      <VanRow v-show="'footer' in $slots" class="detail-footer">
        <slot name="footer" :footer-details="details" />
      </VanRow>
    </VanSkeleton>
  </div>
</template>

<style scoped lang="less">
.main {
  background-color: #f7f8fa;
  overflow-y: auto;
  height: 92vh;
  .skeleton-box {
    margin-top: 1vh;
  }
  .detail-header {
    margin-top: 1vh;
    padding: 1vh 2vh 1vh 2vh;
    background-color: white;
  }
  .detail-body {
    margin-top: 1vh;
    padding: 1vh 2vh 1vh 2vh;
    background-color: white;
  }
  .detail-footer {
    margin-top: 1vh;
    padding: 1vh 2vh 1vh 2vh;
    background-color: white;
  }
}
</style>
