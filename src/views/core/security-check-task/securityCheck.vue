<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 访问用户编号
const userinfoCodeKey = ref('cpi_f_userinfo_code')
// 访问配置名
const configName = ref('GetSafeCheckTaskCRUD')
// 访问api
const api = ref('/api/af-safecheck/logic/commonQuery')
// 服务名称
// const serviceName = ref('af-safecheck')
// :service-name="serviceName"
// 访问路由
const router = useRouter()

// 创建点击函数，进行页面跳转
function toDetail(item) {
  router.push(
    {
      name: 'securityCheckDetail',
      query: {
        id: item[userinfoCodeKey.value],
        api: api.value,
      },
    },
  )
}
</script>

<template>
  <div class="main">
    <!-- 调用XCellList组件渲染列表 -->
    <XCellList
      :config-name="configName"
      @to-detail="toDetail"
    />
  </div>
</template>

<!-- 设置样式 -->
<style lang="less" scoped>
.main {
  padding: var(--base-interval-1);

  :deep(.van-search) {
    padding: 0;
  }
}
</style>
