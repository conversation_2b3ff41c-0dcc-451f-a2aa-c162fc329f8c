<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { getConfigByName } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import { useUserStore } from '@af-mobile-client-vue3/stores/modules/user'
import {
  showDialog,
  showNotify,
  Badge as VanBadge,
  Dialog as VanDialog,
  Field as VanField,
  Icon as VanIcon,
  Radio as VanRadio,
  RadioGroup as VanRadioGroup,
} from 'vant'
import { defineEmits, onMounted, onUnmounted, reactive, ref } from 'vue'

// 定义事件
const emit = defineEmits(['deleteRow'])
const userState = useUserStore().getLogin()
let timer: number | null = null
const configName = ref('ReservationOrderCRUD')
const serviceName = ref('af-safecheck')

// 获取组件引用
const cellListRef = ref()
const statistics = reactive({
  awaiting_security: 0,
  pending: 0,
})

// 列表固定查询条件
const fixQueryForm = { os_f_urban_area: userState.f.resources.orgs, os_f_accept_user_id: userState.f.resources.id }

// 处理公告点击
function handleBadgeClick(opt) {
  const condition = {} as any
  console.log(condition)
  if (opt === 'backlog') {
    condition.os_f_accept_status = '待受理'
    condition.os_f_accept_user_id = null
  }
  else {
    condition.os_f_accept_status = '待安检'
    condition.os_f_accept_user_id = userState.f.resources.id
  }
  console.log(condition)
  cellListRef.value?.updateConditionAndRefresh(condition)
}

// 获取待办数据
function fetchTodoData() {
  post(`/af-safecheck/logic/getToDoSafeOrder`, { params: { userid: userState.f.resources.id, f_urban_area: userState.f.resources.orgs } }).then((res) => {
    console.log('res====', res)
    if (res && res.length > 0) {
      statistics.awaiting_security = res[0].awaiting_security || 0
      statistics.pending = res[0].pending || 0
    }
  })
}

onMounted(() => {
  // 立即执行一次
  fetchTodoData()
  getConfig()
  // 设置定时器，每10秒执行一次
  timer = window.setInterval(() => {
    fetchTodoData()
  }, 10000)
  window.addEventListener('appstate-change', (e: any) => {
    if (e.detail.appState === 'afterhidden') {
      if (timer) {
        clearInterval(timer)
        timer = null
      }
    }
  })
})

// 组件销毁时清理定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 添加新的状态变量
const showCompleteDialog = ref(false)
const showInvalidateDialog = ref(false)
const currentItem = ref(null)
const completeForm = reactive({
  result: '',
  problemDesc: '',
  remark: '',
})
const invalidateForm = reactive({
  reason: '',
  remark: '',
})

// 安检结果选项
const inspectionResults = ref([])

// 作废原因选项
const invalidateReasons = ref([])

// 开始安检
function accept(item) {
  currentItem.value = item
  showCompleteDialog.value = true
}
// 作废安检
function showInvalidated(item) {
  currentItem.value = item
  showInvalidateDialog.value = true
}
// 完成安检
function completed() {
  if (!completeForm.result) {
    showNotify({
      type: 'warning',
      message: '请选择安检结果',
      duration: 2000,
    })
    return false
  }

  const currentResult = inspectionResults.value.find(item => item.value === completeForm.result)
  if (currentResult?.showProblemDesc && !completeForm.problemDesc) {
    showNotify({
      type: 'warning',
      message: '请填写问题描述',
      duration: 2000,
    })
    return false
  }

  // TODO: 调用后端API保存安检结果
  post('/af-safecheck/entity/save/t_order_safecheck', {
    id: currentItem.value.os_id,
    f_accept_status: '已安检',
    f_check_result: completeForm.result,
    f_problem_desc: completeForm.problemDesc || null,
    f_remark: completeForm.remark || null,
    version: currentItem.value.os_version,
    f_accept_over_date: formatDate(new Date()),
  }).then(() => {
    showNotify({
      type: 'success',
      message: '安检完成',
      duration: 2000,
    })
    // 刷新列表
    cellListRef.value?.updateConditionAndRefresh()
    fetchTodoData()
    // 关闭弹窗并重置表单
    showCompleteDialog.value = false
    resetCompleteForm()
  })
  return true
}
function formatDate(date) {
  const Y = date.getFullYear()
  const M = date.getMonth() + 1
  const D = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  return `${Y}-${M < 10 ? (`0${M}`) : M}-${D < 10 ? (`0${D}`) : D} ${hour < 10 ? (`0${hour}`) : hour}:${minute < 10 ? (`0${minute}`) : minute}:${second < 10 ? (`0${second}`) : second}`
}
// 作废安检
function invalidated() {
  if (!invalidateForm.reason) {
    showNotify({
      type: 'warning',
      message: '请选择作废原因',
      duration: 2000,
    })
    return false
  }

  // TODO: 调用后端API保存作废信息
  post('/af-safecheck/entity/save/t_order_safecheck', {
    id: currentItem.value.os_id,
    f_accept_status: '已作废',
    f_problem_desc: `${invalidateForm.reason}-${invalidateForm.remark}`,
    version: currentItem.value.os_version,
  }).then(() => {
    showNotify({
      type: 'success',
      message: '安检已作废',
      duration: 2000,
    })
    // 刷新列表
    cellListRef.value?.updateConditionAndRefresh()
    fetchTodoData()
    // 关闭弹窗并重置表单
    showInvalidateDialog.value = false
    resetInvalidateForm()
  })
  return true
}

function accepted(item) {
  // TODO: 调用后端API保存作废信息
  post('/af-safecheck/entity/save/t_order_safecheck', {
    id: item.os_id,
    f_accept_status: '待安检',
    f_accept_user_id: userState.f.resources.id,
    f_accept_user_name: userState.f.resources.name,
    f_accept_user_phone: userState.f.resources.f_user_telephone,
    version: item.os_version,
  }).then(() => {
    showNotify({
      type: 'success',
      message: '已成功受理，请及时安检！',
      duration: 2000,
    })
    // 刷新列表
    cellListRef.value?.updateConditionAndRefresh()
    fetchTodoData()
  })
  return true
}
function cancelAccept(item) {
  // TODO: 调用后端API保存作废信息
  post('/af-safecheck/entity/save/t_order_safecheck', {
    id: item.os_id,
    f_accept_status: '待受理',
    f_accept_user_id: null,
    f_accept_user_name: null,
    f_accept_user_phone: null,
    version: item.os_version,
  }).then(() => {
    showNotify({
      type: 'success',
      message: '已成功取消受理！',
      duration: 2000,
    })
    // 刷新列表
    cellListRef.value?.updateConditionAndRefresh()
    fetchTodoData()
  })
  return true
}
function getConfig() {
  getConfigByName('invalidateReasonsConfig', (result) => {
    if (result?.value)
      invalidateReasons.value = result.value
  }, 'af-safecheck')
  getConfigByName('inspectionResultsConfig', (result) => {
    if (result?.value)
      inspectionResults.value = result.value
  }, 'af-safecheck')
}
// 重置完成安检表单
function resetCompleteForm() {
  completeForm.result = ''
  completeForm.problemDesc = ''
  completeForm.remark = ''
}

// 重置作废安检表单
function resetInvalidateForm() {
  invalidateForm.reason = ''
  invalidateForm.remark = ''
}
// 退出登录
async function exit_login() {
  showDialog({
    title: '提示',
    message: '确定要退出登录吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
  }).then(async () => {
    await useUserStore().logout()
  }).catch(() => {
    // 用户点击取消，不做任何操作
  })
}
</script>

<template>
  <div class="xlv_t">
    <XCellList
      ref="cellListRef"
      :config-name="configName"
      :service-name="serviceName"
      :fix-query-form="fixQueryForm"
      @accept="accepted"
      @completed="accept"
      @invalidated="showInvalidated"
      @cancel-accept="cancelAccept"
    >
      <template #search-left-loginout>
        <VanBadge @click="exit_login()">
          <VanIcon name="arrow-left" size="24" />
        </VanBadge>
      </template>
      <template #search-right-bad>
        <VanBadge :content="statistics.pending" @click="handleBadgeClick('backlog')">
          <VanIcon name="bullhorn-o" size="24" />
        </VanBadge>
      </template>
      <template #search-right-my>
        <VanBadge :content="statistics.awaiting_security" @click="handleBadgeClick('my')">
          <VanIcon name="description-o" size="24" />
        </VanBadge>
      </template>
    </XCellList>

    <!-- 完成安检弹窗 -->
    <VanDialog
      v-model:show="showCompleteDialog"
      title="完成安检"
      show-cancel-button
      :close-on-click-overlay="false"
      :close-on-popstate="false"
      :before-close="(action) => action === 'confirm' ? completed() : true"
      class="inspection-dialog"
      @cancel="resetCompleteForm"
    >
      <div class="dialog-content">
        <div class="form-item">
          <div class="form-label">
            安检结果
          </div>
          <VanRadioGroup v-model="completeForm.result" class="radio-group">
            <VanRadio
              v-for="item in inspectionResults"
              :key="item.value"
              :name="item.value"
              class="radio-item"
            >
              {{ item.label }}
            </VanRadio>
          </VanRadioGroup>
        </div>

        <div v-if="inspectionResults.find(item => item.value === completeForm.result)?.showProblemDesc" class="form-item">
          <div class="form-label">
            问题描述
          </div>
          <VanField
            v-model="completeForm.problemDesc"
            type="textarea"
            rows="3"
            placeholder="请详细描述发现的问题"
            class="field-item"
          />
        </div>

        <div class="form-item">
          <div class="form-label">
            备注信息
          </div>
          <VanField
            v-model="completeForm.remark"
            type="textarea"
            rows="3"
            placeholder="请输入其他备注信息（选填）"
            class="field-item"
          />
        </div>
      </div>
    </VanDialog>

    <!-- 作废安检弹窗 -->
    <VanDialog
      v-model:show="showInvalidateDialog"
      title="作废安检"
      show-cancel-button
      :close-on-click-overlay="false"
      :close-on-popstate="false"
      :before-close="(action) => action === 'confirm' ? invalidated() : true"
      class="inspection-dialog"
      @cancel="resetInvalidateForm"
    >
      <div class="dialog-content">
        <div class="form-item">
          <div class="form-label">
            作废原因
          </div>
          <VanRadioGroup v-model="invalidateForm.reason" class="radio-group">
            <VanRadio
              v-for="item in invalidateReasons"
              :key="item.value"
              :name="item.value"
              class="radio-item"
            >
              {{ item.label }}
            </VanRadio>
          </VanRadioGroup>
        </div>

        <div class="form-item">
          <div class="form-label">
            补充说明
          </div>
          <VanField
            v-model="invalidateForm.remark"
            type="textarea"
            rows="3"
            placeholder="请详细说明作废原因（选填）"
            class="field-item"
          />
        </div>
      </div>
    </VanDialog>
  </div>
</template>

<style scoped lang="less">
.xlv_t {
  --van-cell-horizontal-padding: 0;
  --van-cell-vertical-padding: 0;
  .dialog-content {
    padding: 8%;
    .form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-label {
      font-size: 14px;
      color: #323233;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .radio-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .radio-item {
      font-size: 14px;
      color: #323233;
    }

    .field-item {
      :deep(.van-field__control) {
        min-height: 80px;
        border: 1px solid #ebedf0;
        border-radius: 4px;
        padding: 8px 12px;
        background-color: #f7f8fa;
      }

      :deep(.van-field__placeholder) {
        color: #969799;
      }
    }
  }
}
</style>
