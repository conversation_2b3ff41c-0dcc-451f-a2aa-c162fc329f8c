<script setup lang="ts">
import {watchEffect} from "vue";
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const idKey = ref('o_id')
const configName = ref('GetSafeCheckTaskCRUD')
function toDetail () {
  router.push({
    name: 'XCellDetailView',
    params: { id: 1 }, // 如果使用命名路由，推荐使用路由参数而不是直接构建 URL
  })
}
</script>

<template>
  <NormalDataLayout id="XCellListView" title="工作计划">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :id-key="idKey"
        serviceName="af-safecheck"
        @to-detail="toDetail"
      />
    </template>
  </NormalDataLayout>
</template>
<style scoped>

</style>