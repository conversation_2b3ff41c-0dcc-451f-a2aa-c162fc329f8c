<script setup lang="ts">
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const formData = reactive({
  thirdPartyPayment: '', // 支持第三方缴费
  limitType: '', // 限购类型
  paymentLimit: '', // 支付限制
  limitCycle: '', // 限购周期
  executeTime: '', // 执行时间
  limitCount: '' // 限购次数
})

// 控制弹窗显示
const showThirdPartyPicker = ref(false)
const showLimitTypePicker = ref(false)
const showPaymentLimitPicker = ref(false)
const showLimitCyclePicker = ref(false)
const showDatePicker = ref(false)

// 选项数据
const thirdPartyOptions = [
  { text: '是', value: 'yes' },
  { text: '否', value: 'no' }
]

const limitTypeOptions = [
  { text: '按次量', value: 'count' },
  { text: '按总量', value: 'total' }
]

const paymentLimitOptions = [
  { text: '气量', value: 'gas' },
  { text: '金额', value: 'amount' }
]

const limitCycleOptions = [
  { text: '按日', value: 'daily' },
  { text: '按月', value: 'monthly' }
]

// 当前日期
const currentDate = ref(new Date())

// 选择器确认事件
const onThirdPartyConfirm = ({ selectedOptions }) => {
  formData.thirdPartyPayment = selectedOptions[0]?.text || ''
  showThirdPartyPicker.value = false
}

const onLimitTypeConfirm = ({ selectedOptions }) => {
  formData.limitType = selectedOptions[0]?.text || ''
  showLimitTypePicker.value = false
}

const onPaymentLimitConfirm = ({ selectedOptions }) => {
  formData.paymentLimit = selectedOptions[0]?.text || ''
  showPaymentLimitPicker.value = false
}

const onLimitCycleConfirm = ({ selectedOptions }) => {
  formData.limitCycle = selectedOptions[0]?.text || ''
  showLimitCyclePicker.value = false
}

const onDateConfirm = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  formData.executeTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  showDatePicker.value = false
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 提交表单
const onSubmit = () => {
  // 验证必填项
  if (!formData.thirdPartyPayment) {
    showToast('请选择是否支持第三方缴费')
    return
  }
  if (!formData.limitType) {
    showToast('请选择限购类型')
    return
  }
  if (!formData.paymentLimit) {
    showToast('请选择支付限制')
    return
  }
  if (!formData.limitCycle) {
    showToast('请选择限购周期')
    return
  }
  if (!formData.executeTime) {
    showToast('请选择执行时间')
    return
  }
  if (!formData.limitCount) {
    showToast('请输入限购次数')
    return
  }

  // 这里可以添加提交逻辑
  console.log('提交的表单数据:', formData)
  showToast('提交成功')
}
</script>

<template>
  <div class="third-party-payment">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="新增补充协议"
      left-arrow
      @click-left="goBack"
    />

    <!-- 主要内容 -->
    <div class="content">
      <!-- 报装信息和补充协议标签 -->
      <van-tabs v-model:active="0" class="custom-tabs">
        <van-tab title="报装信息" />
        <van-tab title="补充协议" />
      </van-tabs>

      <!-- 补充协议表单 -->
      <div class="form-section">
        <div class="section-title">
          <div class="blue-line"></div>
          <span>补充协议</span>
        </div>

        <van-form @submit="onSubmit">
          <van-cell-group class="form-group">
            <!-- 支持第三方缴费 -->
            <van-field
              v-model="formData.thirdPartyPayment"
              is-link
              readonly
              required
              label="支持第三方缴费"
              placeholder="请选择"
              @click="showThirdPartyPicker = true"
            />

            <!-- 限购类型 -->
            <van-field
              v-model="formData.limitType"
              is-link
              readonly
              required
              label="限购类型"
              placeholder="请选择"
              @click="showLimitTypePicker = true"
            />

            <!-- 支付限制 -->
            <van-field
              v-model="formData.paymentLimit"
              is-link
              readonly
              required
              label="支付限制"
              placeholder="请选择"
              @click="showPaymentLimitPicker = true"
            />

            <!-- 限购周期 -->
            <van-field
              v-model="formData.limitCycle"
              is-link
              readonly
              required
              label="限购周期"
              placeholder="请选择"
              @click="showLimitCyclePicker = true"
            />

            <!-- 执行时间 -->
            <van-field
              v-model="formData.executeTime"
              is-link
              readonly
              required
              label="执行时间"
              placeholder="请选择执行时间"
              @click="showDatePicker = true"
            />

            <!-- 限购次数 -->
            <van-field
              v-model="formData.limitCount"
              type="number"
              required
              label="限购次数"
              placeholder="请输入限购次数"
            />
          </van-cell-group>
        </van-form>
      </div>
    </div>

    <!-- 底部提交按钮 -->
    <div class="submit-section">
      <van-button
        type="primary"
        block
        round
        @click="onSubmit"
      >
        提交
      </van-button>
    </div>

    <!-- 选择器弹窗 -->
    <!-- 支持第三方缴费选择器 -->
    <van-popup v-model:show="showThirdPartyPicker" position="bottom">
      <van-picker
        :columns="thirdPartyOptions"
        @confirm="onThirdPartyConfirm"
        @cancel="showThirdPartyPicker = false"
      />
    </van-popup>

    <!-- 限购类型选择器 -->
    <van-popup v-model:show="showLimitTypePicker" position="bottom">
      <van-picker
        :columns="limitTypeOptions"
        @confirm="onLimitTypeConfirm"
        @cancel="showLimitTypePicker = false"
      />
    </van-popup>

    <!-- 支付限制选择器 -->
    <van-popup v-model:show="showPaymentLimitPicker" position="bottom">
      <van-picker
        :columns="paymentLimitOptions"
        @confirm="onPaymentLimitConfirm"
        @cancel="showPaymentLimitPicker = false"
      />
    </van-popup>

    <!-- 限购周期选择器 -->
    <van-popup v-model:show="showLimitCyclePicker" position="bottom">
      <van-picker
        :columns="limitCycleOptions"
        @confirm="onLimitCycleConfirm"
        @cancel="showLimitCyclePicker = false"
      />
    </van-popup>

    <!-- 日期时间选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        title="选择执行时间"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
.third-party-payment {
  min-height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding-bottom: 80px;
}

.custom-tabs {
  background: white;
  
  :deep(.van-tab) {
    color: #969799;
    
    &.van-tab--active {
      color: #1989fa;
      border-bottom: 2px solid #1989fa;
    }
  }
  
  :deep(.van-tabs__line) {
    background-color: #1989fa;
  }
}

.form-section {
  margin-top: 12px;
  background: white;
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.blue-line {
  width: 4px;
  height: 16px;
  background-color: #1989fa;
  margin-right: 8px;
  border-radius: 2px;
}

.form-group {
  border-radius: 8px;
  overflow: hidden;
  
  :deep(.van-cell) {
    padding: 16px;
    
    &:not(:last-child)::after {
      border-bottom: 1px solid #ebedf0;
    }
  }
  
  :deep(.van-field__label) {
    color: #323233;
    font-weight: 500;
  }
  
  :deep(.van-field__control) {
    color: #646566;
  }
  
  :deep(.van-field__control::placeholder) {
    color: #c8c9cc;
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
  
  .van-button {
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;
  
  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }
  
  .van-icon {
    color: white;
  }
}

:deep(.van-picker) {
  .van-picker__toolbar {
    .van-picker__cancel,
    .van-picker__confirm {
      color: #1989fa;
    }
  }
}
</style>
