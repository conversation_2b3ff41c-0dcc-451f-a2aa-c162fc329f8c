<script setup lang="ts">
import { http } from '@af-mobile-client-vue3/utils/http'
import { showDialog, showToast } from 'vant'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

// 定义类型接口
interface LimitPurchaseItem {
  id: string
  contractType: string
  contractStatus: string
  contractAmount: number
  amountUnit: string
  actualSignTime: string
  invalidTime: string
  invalidReason: string
  canCancel: boolean
}

interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

interface ListResponse {
  list: LimitPurchaseItem[]
  total: number
}

// 响应式数据
const searchValue = ref('')
const limitPurchaseList = ref<LimitPurchaseItem[]>([])
const loading = ref(false)
const finished = ref(false)

// 计算属性：过滤后的列表
const filteredList = computed(() => {
  if (!searchValue.value) {
    return limitPurchaseList.value
  }
  return limitPurchaseList.value.filter(item =>
    item.id.includes(searchValue.value)
    || item.contractType.includes(searchValue.value)
    || item.contractStatus.includes(searchValue.value),
  )
})

// 获取限购列表数据
async function fetchLimitPurchaseList(params = {}) {
  try {
    loading.value = true
    const response = await http.request<ApiResponse<ListResponse>>({
      url: '/api/limit-purchase/list',
      method: 'get',
      params: {
        page: 1,
        pageSize: 20,
        searchKeyword: searchValue.value,
        ...params,
      },
    })

    if (response.code === 200) {
      limitPurchaseList.value = response.data.list || []
      finished.value = response.data.total <= limitPurchaseList.value.length
    }
    else {
      showToast(response.message || '获取数据失败')
    }
  }
  catch (error) {
    console.error('获取限购列表失败:', error)
    showToast('网络错误，请稍后重试')
    // 如果API失败，使用模拟数据
    limitPurchaseList.value = [
      {
        id: '11059035',
        contractType: '施工合同',
        contractStatus: '有效',
        contractAmount: 432,
        amountUnit: '韩佰套拾贰...',
        actualSignTime: '2025-07-25 00:00:00',
        invalidTime: '---',
        invalidReason: '---',
        canCancel: true,
      },
      {
        id: '11059034',
        contractType: '施工合同',
        contractStatus: '无效',
        contractAmount: 30,
        amountUnit: '叁拾元整',
        actualSignTime: '2025-07-25 00:00:00',
        invalidTime: '2025-07-25 23:59:59',
        invalidReason: '4234234',
        canCancel: false,
      },
    ]
  }
  finally {
    loading.value = false
  }
}

// 搜索防抖
const searchDebounce = debounce(() => {
  fetchLimitPurchaseList()
}, 500)

// 监听搜索值变化
watch(searchValue, () => {
  searchDebounce()
})

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 页面初始化
onMounted(() => {
  fetchLimitPurchaseList()
})

// 返回上一页
function goBack() {
  history.back()
}

// 跳转到新增限购页面
const router = useRouter()
function goToAdd() {
  console.log('点击了加号按钮') // 调试用
  router.push({ name: 'restricted-purchases' })
}

// 作废操作
async function handleInvalid(item: LimitPurchaseItem) {
  try {
    await showDialog({
      title: '确认作废',
      message: `确定要作废合同 ${item.id} 吗？`,
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })

    // 调用作废API
    const response = await http.request<ApiResponse>({
      url: '/api/limit-purchase/invalid',
      method: 'post',
      data: { id: item.id },
    })

    if (response.code === 200) {
      showToast('作废成功')
      item.contractStatus = '无效'
      item.canCancel = false
      item.invalidTime = new Date().toLocaleString()
    }
    else {
      showToast(response.message || '作废失败')
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('作废操作失败:', error)
      showToast('操作失败，请稍后重试')
    }
  }
}

// 解除限购操作
async function handleRemoveLimit(item: LimitPurchaseItem) {
  try {
    await showDialog({
      title: '解除限购',
      message: `确定要解除合同 ${item.id} 的限购吗？`,
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })

    // 调用解除限购API
    const response = await http.request<ApiResponse>({
      url: '/api/limit-purchase/remove',
      method: 'post',
      data: { id: item.id },
    })

    if (response.code === 200) {
      showToast('解除限购成功')
      // 重新获取列表数据
      fetchLimitPurchaseList()
    }
    else {
      showToast(response.message || '解除限购失败')
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('解除限购失败:', error)
      showToast('操作失败，请稍后重试')
    }
  }
}
</script>

<template>
  <div class="limit-purchase-list">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="补充协议"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <div class="nav-right">
          <van-icon name="filter-o" size="20" class="filter-icon" />
        </div>
      </template>
    </van-nav-bar>

    <!-- 搜索框和新增按钮 -->
    <div class="search-container">
      <van-search
        v-model="searchValue"
        placeholder="综合查询框..."
        shape="round"
        background="#f7f8fa"
      />
      <div class="add-button" @click="goToAdd">
        <van-icon name="plus" size="18" />
      </div>
    </div>

    <!-- 列表内容 -->
    <div class="list-container">
      <!-- 加载状态 -->
      <van-loading v-if="loading" class="loading-container" vertical>
        加载中...
      </van-loading>

      <!-- 列表项 -->
      <div
        v-for="item in filteredList"
        :key="item.id"
        class="list-item"
      >
        <!-- 合同编号 -->
        <div class="item-header">
          <span class="contract-id">{{ item.id }}</span>
        </div>

        <!-- 合同信息 -->
        <div class="item-content">
          <div class="info-row">
            <div class="info-item">
              <span class="label">合同类型:</span>
              <span class="value">{{ item.contractType }}</span>
            </div>
            <div class="info-item">
              <span class="label">合同状态:</span>
              <span class="value" :class="{ 'status-invalid': item.contractStatus === '无效' }">
                {{ item.contractStatus }}
              </span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">合同金额:</span>
              <span class="value">{{ item.contractAmount }}</span>
            </div>
            <div class="info-item">
              <span class="label">金额大写:</span>
              <span class="value">{{ item.amountUnit }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label">实际签订时间:</span>
              <span class="value">{{ item.actualSignTime }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label">作废时间:</span>
              <span class="value">{{ item.invalidTime }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label">作废原因:</span>
              <span class="value">{{ item.invalidReason }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="item-actions">
          <div class="action-left">
            <span class="remove-limit" @click="handleRemoveLimit(item)">解除限购</span>
          </div>
          <div class="action-right">
            <van-button
              v-if="item.canCancel"
              type="primary"
              size="small"
              @click="handleInvalid(item)"
            >
              作废
            </van-button>
          </div>
        </div>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-tip">
        <span>已加载全部内容，如需新增请点击右上角的 + 号</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limit-purchase-list {
  min-height: 100vh;
  background: linear-gradient(180deg, #f7f8fa 0%, #f0f2f5 100%);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;

  .filter-icon {
    color: #666;
  }
}

.search-container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  gap: 12px;

  :deep(.van-search) {
    flex: 1;
    padding: 0;
  }

  .add-button {
    width: 36px;
    height: 36px;
    background: #323233;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    .van-icon {
      color: white;
    }

    &:hover {
      background: #646566;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.loading-container {
  padding: 40px;
  text-align: center;
}

.list-container {
  padding: 8px 16px 16px;
}

.list-item {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.item-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f5f5f5;

  .contract-id {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: 0.5px;
  }
}

.item-content {
  .info-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    align-items: flex-start;

    &.full-width {
      flex: 1 1 100%;
    }

    .label {
      color: #666;
      font-size: 14px;
      margin-right: 8px;
      white-space: nowrap;
      font-weight: 500;
      min-width: 80px;
    }

    .value {
      color: #333;
      font-size: 14px;
      line-height: 1.4;

      &.status-invalid {
        color: #ff4757;
        font-weight: 600;
      }
    }
  }
}

.item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  .action-left {
    .remove-limit {
      color: #1890ff;
      font-size: 14px;
      cursor: pointer;
      font-weight: 500;
      transition: color 0.3s ease;

      &:hover {
        color: #40a9ff;
      }

      &:active {
        color: #096dd9;
      }
    }
  }

  .action-right {
    .van-button {
      height: 36px;
      padding: 0 20px;
      border-radius: 18px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(25, 137, 250, 0.3);

      &:hover {
        box-shadow: 0 4px 12px rgba(25, 137, 250, 0.4);
      }
    }
  }
}

.bottom-tip {
  text-align: center;
  padding: 30px 20px;
  color: #999;
  font-size: 13px;
  background: linear-gradient(to bottom, transparent, #f7f8fa);
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }

  .van-icon {
    color: white;
  }
}

:deep(.van-search) {
  .van-search__content {
    background-color: white;
  }
}
</style>
