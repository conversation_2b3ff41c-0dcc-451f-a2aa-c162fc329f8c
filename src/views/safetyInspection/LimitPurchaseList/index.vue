<script setup lang="ts">
// 模拟数据
const searchValue = ref('')
const limitPurchaseList = ref([
  {
    id: '11059035',
    contractType: '施工合同',
    contractStatus: '有效',
    contractAmount: 432,
    amountUnit: '韩佰套拾贰...',
    actualSignTime: '2025-07-25 00:00:00',
    invalidTime: '---',
    invalidReason: '---',
    canCancel: true
  },
  {
    id: '11059034',
    contractType: '施工合同',
    contractStatus: '无效',
    contractAmount: 30,
    amountUnit: '叁拾元整',
    actualSignTime: '2025-07-25 00:00:00',
    invalidTime: '2025-07-25 23:59:59',
    invalidReason: '4234234',
    canCancel: false
  }
])

const filteredList = computed(() => {
  if (!searchValue.value) {
    return limitPurchaseList.value
  }
  return limitPurchaseList.value.filter(item =>
    item.id.includes(searchValue.value) ||
    item.contractType.includes(searchValue.value) ||
    item.contractStatus.includes(searchValue.value)
  )
})

// 返回上一页
const goBack = () => {
  history.back()
}

// 跳转到新增限购页面
const goToAdd = () => {
  const router = useRouter()
  router.push({ name: 'restricted-purchases' })
}

// 作废操作
const handleInvalid = (item) => {
  showDialog({
    title: '确认作废',
    message: `确定要作废合同 ${item.id} 吗？`,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    // 这里可以调用API进行作废操作
    showToast('作废成功')
    item.contractStatus = '无效'
    item.canCancel = false
  }).catch(() => {
    // 用户取消
  })
}

// 解除限购操作
const handleRemoveLimit = (item) => {
  showDialog({
    title: '解除限购',
    message: `确定要解除合同 ${item.id} 的限购吗？`,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    // 这里可以调用API进行解除限购操作
    showToast('解除限购成功')
    // 可以从列表中移除或更新状态
  }).catch(() => {
    // 用户取消
  })
}
</script>

<template>
  <div class="limit-purchase-list">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="补充协议"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <div class="nav-right">
          <span class="add-text">新增限购</span>
          <van-icon
            name="plus"
            size="20"
            class="add-icon"
            @click="goToAdd"
          />
          <van-icon name="filter-o" size="20" class="filter-icon" />
        </div>
      </template>
    </van-nav-bar>

    <!-- 搜索框 -->
    <div class="search-container">
      <van-search
        v-model="searchValue"
        placeholder="综合查询框..."
        shape="round"
        background="#f7f8fa"
      />
    </div>

    <!-- 列表内容 -->
    <div class="list-container">
      <div
        v-for="item in filteredList"
        :key="item.id"
        class="list-item"
      >
        <!-- 合同编号 -->
        <div class="item-header">
          <span class="contract-id">{{ item.id }}</span>
        </div>

        <!-- 合同信息 -->
        <div class="item-content">
          <div class="info-row">
            <div class="info-item">
              <span class="label">合同类型:</span>
              <span class="value">{{ item.contractType }}</span>
            </div>
            <div class="info-item">
              <span class="label">合同状态:</span>
              <span class="value" :class="{ 'status-invalid': item.contractStatus === '无效' }">
                {{ item.contractStatus }}
              </span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">合同金额:</span>
              <span class="value">{{ item.contractAmount }}</span>
            </div>
            <div class="info-item">
              <span class="label">金额大写:</span>
              <span class="value">{{ item.amountUnit }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label">实际签订时间:</span>
              <span class="value">{{ item.actualSignTime }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label">作废时间:</span>
              <span class="value">{{ item.invalidTime }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label">作废原因:</span>
              <span class="value">{{ item.invalidReason }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="item-actions">
          <div class="action-left">
            <span class="remove-limit" @click="handleRemoveLimit(item)">解除限购</span>
          </div>
          <div class="action-right">
            <van-button
              v-if="item.canCancel"
              type="primary"
              size="small"
              @click="handleInvalid(item)"
            >
              作废
            </van-button>
          </div>
        </div>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-tip">
        <span>已加载全部内容，如需新增请点击右上角的 + 号</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limit-purchase-list {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;

  .add-text {
    color: #ff4444;
    font-size: 14px;
  }

  .add-icon {
    color: #ff4444;
    background: white;
    border: 1px solid #ff4444;
    border-radius: 4px;
    padding: 2px;
  }

  .filter-icon {
    color: #666;
  }
}

.search-container {
  padding: 12px 16px;
  background: white;
}

.list-container {
  padding: 0 16px;
}

.list-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.item-header {
  margin-bottom: 12px;

  .contract-id {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }
}

.item-content {
  .info-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;

    &.full-width {
      flex: 1 1 100%;
    }

    .label {
      color: #646566;
      font-size: 14px;
      margin-right: 4px;
      white-space: nowrap;
    }

    .value {
      color: #323233;
      font-size: 14px;

      &.status-invalid {
        color: #ee0a24;
      }
    }
  }
}

.item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;

  .action-left {
    .remove-limit {
      color: #ff4444;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .action-right {
    .van-button {
      height: 32px;
      padding: 0 16px;
    }
  }
}

.bottom-tip {
  text-align: center;
  padding: 20px;
  color: #969799;
  font-size: 14px;
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }

  .van-icon {
    color: white;
  }
}

:deep(.van-search) {
  .van-search__content {
    background-color: white;
  }
}
</style>
