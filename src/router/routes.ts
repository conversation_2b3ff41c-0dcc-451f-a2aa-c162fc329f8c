import type { RouteRecordRaw } from 'vue-router'
import mixRoutes from '@af-mobile-client-vue3/router/routes'
import my from '@af-mobile-client-vue3/views/user/my/index.vue'
import SecurityCertificate from '@/views/component/SecurityCertificate/index.vue'
import test from '@/views/component/SecurityCertificate/test.vue'
import securityCheck from '@/views/core/security-check-task/securityCheck.vue'
import securityCheckDetail from '@/views/core/security-check-task/securityCheckDetail.vue'
import temporarySecurityCheck from '@/views/core/temporary-security-check/temporarySecurityCheck.vue'
import example1 from '@/views/example/example1/index.vue'

import example2 from '@/views/example/example2/index.vue'
import exampleExist from '@/views/example/exampleExist/index.vue'
import AppointmentForm from '@/views/mini/AppointmentForm.vue'

import AppointmentHistory from '@/views/mini/AppointmentHistory.vue'
import UserAppointment from '@/views/mini/UserAppointment.vue'
import reservationOrder from '@/views/reservationOrder/index.vue'

// 安检计划
import securityCheckPlan from '@/views/securityCheckPlan/index.vue'

// 限购管理页面
import RestrictedPurchases from '@/views/safetyInspection/RestrictedPurchases/index.vue'
import LimitPurchaseList from '@/views/safetyInspection/LimitPurchaseList/index.vue'

// 安检结果
import securityCheckResults from '@/views/securityCheckResults/securityCheckResults.vue'

const routes: Array<RouteRecordRaw> = [

  {
    path: '/',
    name: 'reservationOrder',
    component: reservationOrder,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example1',
    name: 'example1',
    component: example1,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example-exist/:id',
    name: 'example-exist',
    component: exampleExist,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example2',
    name: 'example2',
    component: example2,
    meta: {
      index: 1,
    },
  },
  {
    path: '/user',
    name: 'user',
    component: my,
    meta: {
      index: 2,
    },
  },
  {
    path: '/test',
    name: 'test',
    component: test,
    meta: {
      index: 0,
    },
  },
  {
    path: '/SecurityCertificate',
    name: 'SecurityCertificate',
    component: SecurityCertificate,
    meta: {
      index: 0,
    },
  },
  {
    path: '/task-home',
    name: 'task-home',
    component: securityCheck,
    meta: {
      index: 0,
    },
  },
  {
    path: '/temporary-security-check',
    name: 'temporary-security-check',
    component: temporarySecurityCheck,
    meta: {
      index: 0,
    },
  },
  {
    path: '/securityCheckDetail',
    name: 'securityCheckDetail',
    component: securityCheckDetail,
    meta: {
      index: 3,
    },
  },
  {
    path: '/securityCheckResults',
    name: 'securityCheckResults',
    component: securityCheckResults,
    meta: {
      index: 3,
    },
  },
  {
    path: '/securityCheckPlan',
    name: 'securityCheckPlan',
    component: securityCheckPlan,
    meta: {
      index: 3,
    },
  },
  {
    path: '/user-appointment/:openid',
    name: 'user-appointment',
    component: UserAppointment,
    meta: {
      title: '用户预约',
    },
  },
  {
    path: '/appointment-form',
    name: 'appointment-form',
    component: AppointmentForm,
    meta: {
      title: '预约安检',
    },
  },
  {
    path: '/appointment-history',
    name: 'appointment-history',
    component: AppointmentHistory,
    meta: {
      title: '预约纪录',
    },
  },
  {
    path: '/restricted-purchases',
    name: 'restricted-purchases',
    component: RestrictedPurchases,
    meta: {
      title: '新增限购',
    },
  },
  {
    path: '/limit-purchase-list',
    name: 'limit-purchase-list',
    component: LimitPurchaseList,
    meta: {
      title: '补充协议',
    },
  },
  /*  {
    path: '/reservationOrder',
    name: 'reservationOrder',
    component: reservationOrder,
    meta: {
      title: '预约待办',
    },
  }, */
  {
    path: '/:catchAll(.*)',
    redirect: {
      name: '404',
    },
  },
]

// 除了元素path 属性是 / 的元素 将公共路由全部混入
if (mixRoutes && mixRoutes.length)
  routes.push(...mixRoutes.filter(route => route.path !== '/' && route.path !== '/:catchAll(.*)'))

export default routes
