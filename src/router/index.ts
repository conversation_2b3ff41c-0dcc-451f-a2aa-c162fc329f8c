import type { EnhancedRouteLocation } from '@af-mobile-client-vue3/router/types'
import useRouteCacheStore from '@af-mobile-client-vue3/stores/modules/routeCache'
import setPageTitle from '@af-mobile-client-vue3/utils/set-page-title'
import NProgress from 'nprogress'
// https://router.vuejs.org/zh/
import { createRouter, createWebHistory } from 'vue-router'
import routes from '@/router/routes'
import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: true, parent: '#safecheck-app' })

const baseUrl = import.meta.env.VITE_APP_PUBLIC_PATH
// 创建路由实例并传递 `routes` 配置
const router = createRouter({
  history: createWebHistory(baseUrl),
  routes,
})

router.beforeEach((to: EnhancedRouteLocation) => {
  NProgress.start()

  // 路由缓存
  const routeCacheStore = useRouteCacheStore()
  routeCacheStore.addRoute(to)

  // 页面 title
  setPageTitle(to.meta.title as string);

  (window as any).microApp?.forceDispatch({ title: to.meta.title })
})

router.afterEach(() => {
  NProgress.done()
})

// 导出路由实例，并在 `main.ts` 挂载
export default router
