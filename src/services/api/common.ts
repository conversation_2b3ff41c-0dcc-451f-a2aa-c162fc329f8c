import { post } from '@/services/restTools'
import { indexedDB } from '@af-mobile-client-vue3/utils/indexedDB'

const commonApi = {
  // 获取配置
  getConfig: 'logic/openapi/getLiuliConfiguration',
  // 获取原生配置
  getNativeConfig: 'logic/openapi/getLiuliNativeConfiguration',
  // 配置解析
  parseConfig: 'logic/parseConfig',
  // 通用查询
  query: 'logic/commonQuery',
  // 表单查询
  queryWithResource: 'logic/commonQueryWithResource',
  // 通用新增/修改
  addOrModify: 'logic/commonAddOrModify',
  // 通用删除
  delete: 'logic/commonDelete',
  // 下载数据
  download: 'resource/download',
  // 通用上传
  upload: 'resource/upload',
  // 文件实体操作
  fileEntity: 'entity/t_files',
}

export function getConfigUrl(serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return `/${serviceName}/${commonApi.getConfig}`
}

export function getNativeConfigUrl(serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return `/${serviceName}/${commonApi.getNativeConfig}`
}

export function parseConfigUrl() {
  return `/af-liuli/${commonApi.parseConfig}`
}

/**
 * 获取字典键参数
 */
export function getDictionaryParam() {
  return post('/af-system/logic/getDictionaryParam', {})
}

/**
 * 根据配置名获取配置内容
 * @param content 已有的配置内容
 * @param configName 配置名称
 * @param serviceName 命名空间名称
 * @param callback 回调函数
 */
export function getConfig(content, configName, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME, callback) {
  if (content)
    return content

  indexedDB.getByWeb(configName, getConfigUrl(serviceName), { configName }, callback, null)
}

export function getConfigByName(configName, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME, callback) {
  return getConfig(undefined, configName, serviceName, callback)
}

export function getNativeConfig(configName, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(getNativeConfigUrl(serviceName), { configName })
}

/**
 * 调用Logic获取配置内容
 * @param logicName Logic名称
 * @param parameter Logic调用参数
 * @param serviceName 命名空间名称
 * @param callback 回调函数
 */
export function getConfigByLogic(logicName, parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME, callback) {
  indexedDB.getByWeb(`${logicName}_${JSON.stringify(parameter)}`, `/${serviceName}/logic/${logicName}`, parameter, callback, null)
}

/**
 * 配置解析
 * @param configContent 原配置内容
 * @param configType 配置类型
 */
export function parseConfig(configContent, configType) {
  const url = parseConfigUrl()
  return post(url, {
    configType,
    configContent,
  })
}

/**
 * 通用执行业务逻辑
 */
export function runLogic(logicName, parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(`/${serviceName}/logic/${logicName}`, parameter)
}

/**
 * 通用查询
 */
export function query(parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(`/${serviceName}/${commonApi.query}`, parameter)
}

/**
 * 通用表单查询
 */
export function queryWithResource(parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(`/${serviceName}/${commonApi.queryWithResource}`, parameter)
}

/**
 * 通用新增/修改
 */
export function addOrModify(parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(`/${serviceName}/${commonApi.addOrModify}`, parameter)
}

/**
 * 通用删除
 */
export function remove(parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(`/${serviceName}/${commonApi.delete}`, parameter)
}

// 文件删除
export function fileDelete(parameter, serviceName = import.meta.env.VITE_APP_SYSTEM_NAME) {
  return post(`/${serviceName}/${commonApi.fileEntity}`, parameter)
}

export { commonApi }
