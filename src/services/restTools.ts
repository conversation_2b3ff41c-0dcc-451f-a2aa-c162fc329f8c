import type { BasicResponseModel } from '@/api/user'
import { http } from '@af-mobile-client-vue3/utils/http'

/**
 * GET请求
 * @param url 请求地址
 * @param params 路径参数
 */
export function get(url: string, params?: any) {
  return http.request<BasicResponseModel>({
    url,
    method: 'GET',
    params,
  })
}

/**
 * POST请求
 * @param url 请求地址
 * @param data 请求参数
 */
export function post(url: string, data: any) {
  return http.request<BasicResponseModel>({
    url,
    method: 'POST',
    data,
  })
}
