import md5 from 'md5'

/**
 * MD5 加密函数
 * @param text - 需要加密的文本
 * @returns 32位小写MD5加密字符串
 */
export function encryptMD5(text: string): string {
  return md5(text) as string
}

/**
 * 带盐值的 MD5 加密
 * @param text - 需要加密的文本
 * @param salt - 盐值
 * @returns 32位小写MD5加密字符串
 */
export function encryptMD5WithSalt(text: string, salt: string): string {
  return md5(text + salt) as string
}

// 类型定义
export interface EncryptResult {
  original: string
  encrypted: string
  timestamp: number
}

/**
 * 加密并返回详细信息
 * @param text - 需要加密的文本
 * @param salt - 可选的盐值
 * @returns 包含原始文本、加密结果和时间戳的对象
 */
export function encryptWithDetails(text: string, salt?: string): EncryptResult {
  const encrypted = salt ? encryptMD5WithSalt(text, salt) : encryptMD5(text)
  return {
    original: text,
    encrypted,
    timestamp: Date.now(),
  }
}

// 示例用法：
// const password = '123456';
// const encrypted = encryptMD5(password); // 输出: e10adc3949ba59abbe56e057f20f883e
// const encryptedWithSalt = encryptMD5WithSalt(password, 'mySalt'); // 输出: 带盐值的加密结果
