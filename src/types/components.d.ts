/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanCalendar: typeof import('vant/es')['Calendar']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanCol: typeof import('vant/es')['Col']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDialog: typeof import('vant/es')['Dialog']
    VanDivider: typeof import('vant/es')['Divider']
    VanDropdownItem: typeof import('vant/es')['DropdownItem']
    VanDropdownMenu: typeof import('vant/es')['DropdownMenu']
    VanField: typeof import('vant/es')['Field']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanRow: typeof import('vant/es')['Row']
    VanSearch: typeof import('vant/es')['Search']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
