import type { ConfigEnv, UserConfig } from 'vite'
import path from 'node:path'
import process from 'node:process'
import { loadEnv } from 'vite'
import { createVitePlugins } from './build/vite'
import { exclude, include } from './build/vite/optimize'

export default ({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd()
  const env = loadEnv(mode, root)

  const appProxys = {}

  const v4Server = 'http://*************:31567'
  // const v4Server = 'http://127.0.0.1:9025'

  return {
    base: env.VITE_APP_PUBLIC_PATH,
    plugins: createVitePlugins(mode),

    server: {
      host: true,
      port: 7101,
      proxy: Object.assign({
        '/api': {
          target: v4Server,
          ws: false,
          changeOrigin: true,
        },
        '/resource': {
          // pathRewrite: { '^/resource': '/' },
          target: v4Server,
          changeOrigin: true,
        },
      }, appProxys),
    },

    resolve: {
      alias: {
        '@': path.join(__dirname, './src'),
        '~': path.join(__dirname, './src/assets'),
        '~root': path.join(__dirname, '.'),
        '@af-mobile-client-vue3': path.resolve(__dirname, 'node_modules/af-mobile-client-vue3/src'),
        'xml-utils': path.resolve(__dirname, 'node_modules/xml-utils'),
      },
    },

    build: {
      cssCodeSplit: false,
      chunkSizeWarningLimit: 2048,
      outDir: `./dist/${env.VITE_APP_OUT_DIR}`,
      rollupOptions: {
        output: {
          // 打包时分割资源
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks(id) {
            if (id.includes('node_modules'))
              return 'third' // 代码分割为第三方包

            if (id.includes('views'))
              return 'views' // 代码分割为业务视图
          },
        },
      },
    },

    optimizeDeps: { include, exclude },
  }
}
