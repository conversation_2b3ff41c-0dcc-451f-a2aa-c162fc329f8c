{"name": "af-safecheck-mobile-vue-web", "type": "module", "version": "1.0.0", "packageManager": "pnpm@10.13.1", "description": "Vue + Vite H5 Starter Template", "engines": {"node": ">=20.19.0"}, "scripts": {"dev": "cross-env MOCK_SERVER_PORT=8086 vite", "compress": "node ./compress.js", "build:dev": "vue-tsc --noEmit && vite build --mode development && pnpm run compress", "build:pro": "vue-tsc --noEmit && vite build --mode production && pnpm run compress", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "release": "bumpp --commit --push --tag", "typecheck": "vue-tsc --noEmit", "commitlint": "commitlint --edit", "prepare": "simple-git-hooks"}, "dependencies": {"@iconify/vue": "4.3.0", "@micro-zoe/micro-app": "1.0.0-rc.26", "@unhead/vue": "2.0.12", "@vant/area-data": "^2.0.0", "@vant/touch-emulator": "^1.4.0", "@vant/use": "^1.6.0", "@vueuse/core": "^13.5.0", "af-mobile-client-vue3": "^1.3.4", "axios": "^1.10.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "md5": "^2.3.0", "nprogress": "^0.2.0", "ol": "^10.5.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "resize-detector": "^0.3.0", "store": "^2.0.12", "vant": "^4.9.21", "vconsole": "^3.15.1", "vue": "^3.5.17", "vue-i18n": "^11.1.10", "vue-router": "^4.5.1", "vue3-hash-calendar": "^1.1.3"}, "devDependencies": {"@antfu/eslint-config": "4.17.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@iconify/json": "2.2.318", "@iconify/utils": "^2.3.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.14", "@types/nprogress": "^0.2.3", "@unocss/eslint-config": "66.3.3", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-legacy": "^7.0.1", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "bumpp": "^10.2.0", "consola": "^3.4.2", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "less": "^4.4.0", "lint-staged": "^16.1.2", "mockjs": "^1.1.0", "postcss-mobile-forever": "^5.0.0", "rollup": "^4.45.1", "rollup-plugin-compression": "^1.0.3", "simple-git-hooks": "^2.13.0", "tar": "^7.4.3", "terser": "^5.43.1", "typescript": "^5.8.3", "unocss": "66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-router": "^0.14.0", "vite": "^7.0.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock-dev-server": "^1.9.1", "vite-plugin-pwa": "^1.0.1", "vite-plugin-sitemap": "^0.8.2", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^3.0.2"}, "pnpm": {"allowedDeprecatedVersions": {"glob": "7.2.3", "inflight": "1.0.6", "sourcemap-codec": "1.4.8"}, "peerDependencyRules": {"allowedVersions": {}}, "onlyBuiltDependencies": ["core-js", "esbuild", "simple-git-hooks", "unrs-resolver"]}, "resolutions": {"vite": "^7.0.5"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "commit-msg": "pnpm commitlint $1"}, "lint-staged": {"*": "eslint --fix"}}