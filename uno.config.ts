import * as fs from 'node:fs'

import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
import { createRemToPxProcessor } from '@unocss/preset-wind4/utils'

import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetWind4,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

// 本地 SVG 图标存放目录
const iconsDir = './src/icons/svg'

const BASE_FONT_SIZE = 4

// 读取本地 SVG 目录，自动生成 `safelist`
function generateSafeList() {
  try {
    return fs
      .readdirSync(iconsDir)
      .filter(file => file.endsWith('.svg'))
      .map(file => `i-svg:${file.replace('.svg', '')}`)
  }
  catch (error) {
    console.error('无法读取图标目录:', error)
    return []
  }
}

export default defineConfig({
  shortcuts: [
    ['btn', 'px-6 py-3 rounded-4 border-none inline-block bg-green-400 text-white cursor-pointer outline-hidden hover:bg-green-600 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50'],
  ],
  presets: [
    presetWind4({
      preflights: {
        theme: {
          process: createRemToPxProcessor(BASE_FONT_SIZE),
        },
      },
    }),
    presetAttributify(),
    presetIcons(
      {
        scale: 1.2,
        warn: true,
        extraProperties: {
          'display': 'inline-block',
          'vertical-align': 'middle',
          'width': 'var(--van-font-size-md)',
          'height': 'var(--van-font-size-md)',
        },
        // 注册本地 SVG 图标
        collections: {
          // svg 是图标集合名称，使用 `i-svg:图标名` 调用
          svg: FileSystemIconLoader(iconsDir, (svg) => {
            // 如果 SVG 文件未定义 `fill` 属性，则默认填充 `currentColor`
            // 这样图标颜色会继承文本颜色，方便在不同场景下适配
            return svg.includes('fill="')
              ? svg
              : svg.replace(/^<svg /, '<svg fill="currentColor" ')
          }),
        },
      },
    ),
  ],
  safelist: generateSafeList(), // 动态生成 `safelist`
  postprocess: [
    createRemToPxProcessor(BASE_FONT_SIZE),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
})
