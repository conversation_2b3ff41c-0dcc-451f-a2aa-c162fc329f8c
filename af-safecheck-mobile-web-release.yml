kind: Deployment
apiVersion: apps/v1
metadata:
  name: af-safecheck-mobile-web
  namespace: v4service-release
  labels:
    app: af-safecheck-mobile-web
  annotations:
    deployment.kubernetes.io/revision: '1'
    kubesphere.io/creator: admin
    kubesphere.io/description: V4 安检手机端正式前台
spec:
  replicas: 1
  selector:
    matchLabels:
      app: af-safecheck-mobile-web
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: af-safecheck-mobile-web
      annotations:
        kubesphere.io/creator: admin
        kubesphere.io/imagepullsecrets: '{"af-safecheck-mobile-web":"harbor-inner-server"}'
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: logs
          hostPath:
            path: /var/af/v4service-release/af-safecheck-mobile-web/logs
            type: ''
      containers:
        - name: af-safecheck-mobile-web
          image: >-
            harborcdn.aofengcloud.com/afsoft_client/af-system-vue-web:202502171802
          ports:
            - name: tcp-8080
              containerPort: 8080
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: logs
              mountPath: /var/log/nginx
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor-inner-server
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
