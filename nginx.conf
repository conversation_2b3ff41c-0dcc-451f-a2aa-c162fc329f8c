#user  nobody;
user root;
#工作进程数，一般设置为cpu核心数
worker_processes  2;
worker_rlimit_nofile 65535;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
error_log  /var/log/nginx/error.log  info;

#pid        logs/nginx.pid;


events {
    #==最大连接数，一般设置为cpu*2048
    worker_connections  2048;
}


http {
    #文件扩展名与文件类型映射表
    include       mime.types;

   #关闭自动忽略headers带下划线的参数
   underscores_in_headers on;

    #默认文件类型
    default_type  application/octet-stream;

    #开启高效文件传输模式
    sendfile        on;

    #长连接超时时间，单位是秒
    keepalive_timeout  60;

    #配置日志格式
    log_format  main  escape=json '{ "time": "$year-$month-$day $hour:$minutes:$seconds", '
    '"request_id": "$request_id",'
    '"request": "$request",'
    '"remote_addr": "$remote_addr",'
    '"costime": "$request_time",'
    '"realtime": "$upstream_response_time",'
    '"status": $status,'
    '"x_forwarded": "$http_x_forwarded_for",'
    '"referer": "$http_referer",'
    '"upstr_addr": "$upstream_addr",'
    '"bytes":$body_bytes_sent,'
    '"body": "$request_body",'
    '"agent": "$http_user_agent" },';

    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,Pragma,Cache-Control,If-Modified-Since,token';
    add_header 'Access-Control-Max-Age' 1728000;

    access_log  /var/log/nginx/$year-$month-$day-$hour-access.log  main;

    #启用Gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    #当配置多个server节点时，默认server names的缓存区大小就不够了，需要手动设置大一点
    server_names_hash_bucket_size 512;
    client_max_body_size 10M;

    #server表示虚拟主机可以理解为一个站点，可以配置多个server节点搭建多个站点
    #每一个请求进来确定使用哪个server由server_name确定

    server {
        listen       8080;
        server_name  localhost;

        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})") {
            set $year $1;
            set $month $2;
            set $day $3;
            set $hour $4;
            set $minutes $5;
            set $seconds $6;
        }

        proxy_set_header request_id $request_id;

       # 静态资源转发
        location / {
          alias /usr/local/dist/dist_client/;
          #默认主页
          index  index.html index.htm;
          try_files $uri $uri/ /index.html;
        }
    }
}
