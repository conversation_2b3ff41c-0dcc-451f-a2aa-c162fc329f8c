Before diving into the best practices, please note that you may need to adapt the globs depending on your specific project structure.

---

name: vue-best-practices.mdc
description: Best practices for Vue 3 applications
globs: **/*.{vue,ts,tsx,js,jsx}
---

- Use Composition API for better code organization and reusability
- Implement proper reactivity with `ref` and `reactive`
- Use `<script setup>` for concise component syntax
- Leverage Vue's built-in directives like `v-model` for form handling

---

name: vite-best-practices.mdc
description: Best practices for Vite configuration and optimization
globs: vite.config.{ts,js}
---

- Use `vite-plugin-legacy` for better browser compatibility
- Implement code splitting with dynamic imports
- Utilize `vite-plugin-compression` for production builds
- Configure `vite-plugin-pwa` for progressive web app support

---

name: pinia-best-practices.mdc
description: Best practices for state management with Pinia
globs: **/*.{ts,js}
---

- Use Pinia stores for global state management
- Implement `pinia-plugin-persistedstate` for state persistence
- Organize stores by feature, not by data type
- Use getters for computed properties in stores

---

name: vue-router-best-practices.mdc
description: Best practices for routing with Vue Router
globs: **/*.{vue,ts,js}
---

- Use named routes for better maintainability
- Implement route-level code splitting for performance
- Use navigation guards for route protection and logic
- Leverage the `router-link` component for better performance

---

name: vant-best-practices.mdc
description: Best practices for using Vant UI components
globs: **/*.{vue,ts,js}
---

- Use Vant components for consistent UI across the app
- Customize Vant themes using Less variables
- Implement lazy loading for images in Vant components
- Use Vant's built-in form validation for better user experience

---

name: axios-best-practices.mdc
description: Best practices for HTTP requests with Axios
globs: **/*.{ts,js}
---

- Use interceptors for global request/response handling
- Implement proper error handling and timeouts
- Use `axios.create()` for creating instances with custom configurations
- Leverage `qs` library for proper URL parameter serialization

---

name: typescript-best-practices.mdc
description: TypeScript coding standards and type safety guidelines
globs: **/*.{ts,tsx}
---

- Use strict null checks for better type safety
- Prefer interfaces over types for object shapes
- Implement proper type inference and use type guards
- Use enums for a set of named constants

---

name: eslint-best-practices.mdc
description: Best practices for code linting with ESLint
globs: .eslintrc.{js,cjs}
---

- Extend `@antfu/eslint-config` for Vue 3 projects
- Use `@unocss/eslint-plugin` for UnoCSS-related linting
- Implement `eslint-ts-patch` for better TypeScript support
- Configure rules to enforce consistent coding style across the project
