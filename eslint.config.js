import antfu from '@antfu/eslint-config'

export default antfu(
  {
    vue: true,
    typescript: true,

    // Enable UnoCSS support
    // https://unocss.dev/integrations/vscode
    unocss: true,
    formatters: true,
  },
  {
    // enable UnoCSS support
    // https://unocss.dev/integrations/vscode
    // 禁用对 package.json 中键排序的检查
    rules: {
      'jsonc/sort-keys': 'off',
      'imports/no-unused-vars': 'off',
      'ts/no-unsafe-function-type': 'off',
      'unused-imports/no-unused-vars': 'off',
      'no-console': ['error', { allow: ['warn', 'error', 'log', 'info'] }],
    },
  },
  {
    ignores: [
      '.github/**',
    ],
  },
)
