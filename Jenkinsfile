import java.text.SimpleDateFormat

final String SERVICE_NAME = "af-safecheck-mobile-web"
final String MASTER_IP = "aote-office.8866.org -p 20302"
final String HARBOR = "*************:32767"
final String HARBOR_CDN = "harborcdn.aofengcloud.com"
final String DOCKER_SLAVE_URL = "tcp://*************:2375"
final String DOCKER_WORK_DIR_NAME = "afsoft_client"
final String KUBESPHERE_PROJECT_NAME = "service"
final String DIST_MAIN_PATH = "/var/af/standard/client/phone_dist"

pipeline {
    agent any
    environment {
        // 镜像仓库
        DOCKER_REGISTRY = "https://${HARBOR}"
        HARBOR_URL = "${HARBOR}"
        // 镜像名称
        IMAGE_NAME = "${SERVICE_NAME}"
        WORK_DIR = "${DOCKER_WORK_DIR_NAME}"
    }
    stages {
        stage("build") {
            tools {
              nodejs 'node20'
            }
            steps {
                script {
                    sh "pnpm install --registry https://registry.npmjs.org --proxy http://aote-office.8866.org:31691 --https-proxy http://aote-office.8866.org:31691"
                    sh "pnpm run build:dev"
                    version = "${new SimpleDateFormat('yyyyMMddHHmm').format(new Date())}"
                    env.BUILD_VERSION = version
                }
            }
        }
        stage('ssh') {
            steps {
                script {
                    def remote = [
                        name         : 'aote-office.8866.org',
                        host         : 'aote-office.8866.org',
                        port         : '20302' as int,
                        allowAnyHosts: true
                    ]
                    withCredentials([sshUserPrivateKey(credentialsId: '50.67', keyFileVariable: 'identity', passphraseVariable: '', usernameVariable: 'userName')]) {
                        remote.user = userName
                        remote.identityFile = identity
                        // 更新dist包
                        sshCommand remote: remote, command: "pwd"
                        sshCommand remote: remote, command: "mkdir -p ${DIST_MAIN_PATH}"
                        sshPut remote: remote, from: "dist/dist_${SERVICE_NAME}.tar.gz", into: "${DIST_MAIN_PATH}/"
                        sshCommand remote: remote, command: "cd ${DIST_MAIN_PATH} && rm -rf ${DIST_MAIN_PATH}/dist_${SERVICE_NAME}"
                    }
                }
            }
        }
        stage('docker') {
            steps {
                script {
                    def yamlFile = readYaml file: "${SERVICE_NAME}.yml"
                    yamlFile.metadata['namespace'] = "${KUBESPHERE_PROJECT_NAME}"
                    yamlFile.spec.template.spec.containers[0].image = "${HARBOR_CDN}/${WORK_DIR}/${IMAGE_NAME}:${env.BUILD_VERSION}"
                    writeYaml file: "${SERVICE_NAME}.yml", data: yamlFile, overwrite: true
                    docker.withServer("${DOCKER_SLAVE_URL}") {
                        buildAndPushDockerImage()
                    }
                }
            }
        }
        stage('applyToK8S') {
            steps {
                sshPublisher(publishers: [
                        sshPublisherDesc(
                                configName: 'K8S',
                                transfers: [
                                        sshTransfer(
                                                sourceFiles: "${SERVICE_NAME}.yml",
                                                execCommand: "kubectl apply -f /root/deployment/${SERVICE_NAME}.yml"
                                        )
                                ]
                        )
                ])
            }
        }
    }
}

def buildAndPushDockerImage() {
    try {
        sh "docker build -t ${IMAGE_NAME}:${env.BUILD_VERSION} ."
        withCredentials([usernamePassword(
                credentialsId: 'harbor-auth',
                usernameVariable: 'DOCKER_REGISTRY_USERNAME',
                passwordVariable: 'DOCKER_REGISTRY_PASSWORD'
        )]) {
            sh "docker login -u ${DOCKER_REGISTRY_USERNAME} -p ${DOCKER_REGISTRY_PASSWORD} ${DOCKER_REGISTRY}"
        }
        sh "docker tag ${IMAGE_NAME}:${env.BUILD_VERSION} ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:${env.BUILD_VERSION}"
        sh "docker tag ${IMAGE_NAME}:${env.BUILD_VERSION} ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:latest"
        sh "docker push ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:${env.BUILD_VERSION}"
        sh "docker push ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:latest"
    } finally {
        sh "docker rmi ${IMAGE_NAME}:${env.BUILD_VERSION}"
    }
}
