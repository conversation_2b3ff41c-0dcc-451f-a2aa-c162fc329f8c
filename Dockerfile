# Stage 1: Build stage
FROM nginx:1.29.0-alpine AS build

ARG SERVICE_NAME="af-safecheck-mobile-web"
COPY ./nginx.conf /etc/nginx/nginx.conf
COPY ./dist/dist_${SERVICE_NAME}.tar.gz /usr/local/dist/dist_client/
RUN tar -xzvf /usr/local/dist/dist_client/dist_${SERVICE_NAME}.tar.gz -C /usr/local/dist/dist_client/ && \
    rm /usr/local/dist/dist_client/dist_${SERVICE_NAME}.tar.gz

# Stage 2: Final stage
FROM nginx:1.29.0-alpine


# Copy the extracted files from the build stage to the final stage
COPY --from=build /usr/local/dist/dist_client/ /usr/local/dist/dist_client/
COPY --from=build /etc/nginx/nginx.conf /etc/nginx/nginx.conf

EXPOSE 8080

